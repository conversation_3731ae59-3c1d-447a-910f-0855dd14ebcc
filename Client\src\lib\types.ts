export type Department =
  | "FINANCE"
  | "MINISTRIES"
  | "PENSIONS"
  | "PENTMEDIA"
  | "MISSIONS"
  | "PENTSOS"
  | "AUDIT"
  | "ADMINISTRATOR"
  | "SYSTEM ADMIN";

export type TaxType =
  | ""
  | "NONE"
  | "GOODS 3%"
  | "SERVICE 7.5%"
  | "WORKS 5%"
  | "RENT 8%"
  | "PCC 12.5%"
  | "RISK 5%"
  | "VEH.MAINT 10%"
  | "OTHER";

export type Currency = "GHS" | "USD" | "GBP" | "EUR";

export type ClearanceRemark = "CLEARED" | "REFUNDED TO CHEST" | "DUE STAFF" | "RETURNED";

export type TransactionStatus =
  | "PENDING"
  | "PENDING SUBMISSION"
  | "PENDING RECEIPT"
  | "AUDIT: PROCESSING"
  | "VOUCHER PROCESSING"
  | "VOUCHER CERTIFIED"
  | "VOUCHER REJECTED"
  | "REJECTED: PENDING DISPATCH"
  | "VOUCHER RETURNED"
  | "VOUCHER PENDING RETURN"
  | "PENDING DISPATCH";

export interface User {
  id: string;
  name: string;
  department: Department;
  role: "admin" | "manager" | "operator" | "viewer" | "USER";
}

export interface Voucher {
  id: string;
  voucherId: string;
  date: string;
  claimant: string;
  description: string;
  amount: number;
  currency: Currency;
  department: Department;
  originalDepartment?: Department; // CRITICAL FIX: Track original department for audit workflow
  dispatchedBy: string;
  dispatchTime: string;
  status: TransactionStatus;
  sentToAudit: boolean;
  createdBy: string;
  batchId?: string;
  receivedBy?: string;
  receiptTime?: string;
  receivedByAudit?: boolean;  // CRITICAL FIX: Add missing field for AUDIT acceptance tracking
  comment?: string;
  taxType?: TaxType;
  taxDetails?: string;
  taxAmount?: number;
  preAuditedAmount?: number;
  preAuditedBy?: string;
  certifiedBy?: string;
  auditDispatchTime?: string;
  auditDispatchedBy?: string;

  // WORKFLOW STATE MIGRATION: Add workflow state field
  workflow_state?: string;
  dispatchToOnDepartment?: boolean;
  postProvisionalCash?: boolean;
  dispatched?: boolean;
  dispatchToAuditBy?: string;
  workStarted?: boolean;  // CRITICAL FIX: Track if work has been started on voucher
  isReturned?: boolean;
  returnComment?: string;
  returnTime?: string;
  deleted?: boolean;
  deletionTime?: string;
  rejectionTime?: string;
  departmentReceiptTime?: string;
  departmentReceivedBy?: string;
  departmentRejected?: boolean;
  rejectedBy?: string;
  pendingReturn?: boolean;
  returnInitiatedTime?: string;
  referenceId?: string;
  // New fields for rejected voucher tracking
  rejectedDispatchedBy?: string;
  rejectedDispatchTime?: string;
  rejectedReceivedBy?: string;
  rejectedReceiptTime?: string;
  isRejectedVoucher?: boolean;
  // Rejection workflow fields
  rejectionType?: string;
  isRejectionCopy?: boolean;
  rejectionWorkflowStage?: string;
  parentVoucherId?: string;
  isResubmitted?: boolean; // RESUBMISSION FIX: Track if voucher is a resubmission
  finance_received?: number; // RESUBMISSION FIX: Track if Finance has received the voucher

  // RESUBMISSION OVERRIDE SYSTEM: New visibility flags
  resubmission_certified_visible_to_finance?: number; // Flag for Finance CERTIFIED tab visibility
  resubmission_tracking_visible_to_audit?: number; // Flag for Audit DISPATCHED tab visibility
}

export interface VoucherBatch {
  id: string;
  department: Department;
  voucherIds: string[];
  sentBy: string;
  sentTime: string;
  received: boolean;
  fromAudit?: boolean; // Indicates if the batch was created by Audit to send to department
}

export interface ProvisionalCashRecord {
  id: string;
  voucherId: string;
  voucherRef: string;
  claimant: string;
  description: string;
  mainAmount: number;
  currency: Currency;
  amountRetired?: number;
  clearanceRemark?: ClearanceRemark;
  dateRetired?: string;
  clearedBy?: string;
  comment?: string;
  date: string; // Required field
}

export interface Notification {
  id: string;
  userId: string;
  message: string;
  isRead: boolean;
  timestamp: string;
  voucherId?: string;
  batchId?: string;
  type: "NEW_VOUCHER" | "VOUCHER_REJECTED" | "VOUCHER_CERTIFIED" | "VOUCHER_RETURNED" | "NEW_BATCH" | "RESUBMISSION" | "OTHER";
  fromAudit?: boolean; // Indicates if the notification is for a batch from Audit to department
}

export interface BlacklistedVoucherId {
  id: string;
  voucherId: string;
}
