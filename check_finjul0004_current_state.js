const mysql = require('mysql2/promise');

async function checkFINJUL0004CurrentState() {
  console.log('🔍 CHECKING FINJUL0004 CURRENT STATE AFTER FIX');
  console.log('===============================================');
  
  const connection = await mysql.createConnection({
    host: 'localhost',
    user: 'root',
    password: 'vms@2025@1989',
    database: 'vms_production'
  });
  
  try {
    const [vouchers] = await connection.execute(`
      SELECT id, voucher_id, status, workflow_state, is_resubmitted, 
             certified_by, rejected_by, department, batch_id
      FROM vouchers 
      WHERE voucher_id = 'FINJUL0004'
    `);
    
    if (vouchers.length === 0) {
      console.log('❌ FINJUL0004 not found');
      return;
    }
    
    const voucher = vouchers[0];
    console.log('📋 CURRENT STATE AFTER BATCH RECEIVING:');
    console.log(`   Status: ${voucher.status}`);
    console.log(`   Workflow State: ${voucher.workflow_state}`);
    console.log(`   Department: ${voucher.department}`);
    console.log(`   Is Resubmitted: ${voucher.is_resubmitted}`);
    console.log(`   Certified By: ${voucher.certified_by}`);
    console.log(`   Rejected By: ${voucher.rejected_by}`);
    console.log(`   Batch ID: ${voucher.batch_id}`);
    
    console.log('\n🔍 TAB VISIBILITY ANALYSIS:');
    
    // Check which tabs this voucher should appear in based on workflow_state
    const workflowState = voucher.workflow_state;
    
    if (workflowState === 'AUDIT_DISPATCHED') {
      console.log('✅ DISPATCHED tab: YES (workflow_state = AUDIT_DISPATCHED)');
      console.log('❌ CERTIFIED tab: NO (workflow_state should be FINANCE_CERTIFIED for this tab)');
      console.log('');
      console.log('🔧 ISSUE IDENTIFIED:');
      console.log('   For resubmissions, we need DUAL VISIBILITY:');
      console.log('   - Finance DISPATCHED tab (for tracking dispatch from Audit)');
      console.log('   - Finance CERTIFIED tab (for Finance operations)');
      console.log('');
      console.log('💡 SOLUTION NEEDED:');
      console.log('   The batch receiving logic should set workflow_state to allow');
      console.log('   dual visibility for certified resubmissions.');
    } else if (workflowState === 'FINANCE_CERTIFIED') {
      console.log('✅ CERTIFIED tab: YES (workflow_state = FINANCE_CERTIFIED)');
      console.log('❌ DISPATCHED tab: NO (would need AUDIT_DISPATCHED for this tab)');
    } else if (workflowState === 'FINANCE_REJECTED') {
      console.log('❌ This indicates the fix did NOT work - voucher was processed as rejected');
    } else {
      console.log(`ℹ️  Workflow state: ${workflowState} - needs analysis`);
    }
    
    console.log('\n🎯 NEXT STEPS:');
    if (voucher.status === 'VOUCHER CERTIFIED' && workflowState === 'AUDIT_DISPATCHED') {
      console.log('✅ The batch receiving fix WORKED - voucher was certified!');
      console.log('🔧 Now we need to fix the workflow_state for dual visibility');
      console.log('   Option 1: Set workflow_state to FINANCE_CERTIFIED for resubmissions');
      console.log('   Option 2: Modify tab logic to show resubmissions in both tabs');
    } else if (voucher.status === 'VOUCHER REJECTED') {
      console.log('❌ The fix did NOT work - voucher was still processed as rejected');
    }
    
  } catch (error) {
    console.error('❌ Error checking FINJUL0004 state:', error);
  } finally {
    await connection.end();
  }
}

checkFINJUL0004CurrentState().catch(console.error);
