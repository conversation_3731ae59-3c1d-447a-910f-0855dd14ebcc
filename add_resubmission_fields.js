const mysql = require('mysql2/promise');
require('dotenv').config();

async function addResubmissionFields() {
  let connection;
  
  try {
    // Create connection
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || 'vms@2025@1989',
      database: process.env.DB_NAME || 'vms_production'
    });

    console.log('🔗 Connected to database');

    // Add resubmission fields to vouchers table
    console.log('📝 Adding is_resubmission field to vouchers table...');
    try {
      await connection.execute(`
        ALTER TABLE vouchers 
        ADD COLUMN is_resubmission BOOLEAN DEFAULT FALSE
      `);
      console.log('✅ Added is_resubmission field to vouchers table');
    } catch (error) {
      if (error.code === 'ER_DUP_FIELDNAME') {
        console.log('ℹ️  is_resubmission field already exists in vouchers table');
      } else {
        throw error;
      }
    }

    // Add resubmission fields to voucher_batches table
    console.log('📝 Adding resubmission fields to voucher_batches table...');
    try {
      await connection.execute(`
        ALTER TABLE voucher_batches 
        ADD COLUMN contains_resubmissions BOOLEAN DEFAULT FALSE,
        ADD COLUMN resubmission_count INT DEFAULT 0
      `);
      console.log('✅ Added resubmission fields to voucher_batches table');
    } catch (error) {
      if (error.code === 'ER_DUP_FIELDNAME') {
        console.log('ℹ️  Resubmission fields already exist in voucher_batches table');
      } else {
        throw error;
      }
    }

    // Verify the fields were added
    console.log('🔍 Verifying fields were added...');
    
    const [voucherColumns] = await connection.execute(`
      SHOW COLUMNS FROM vouchers LIKE 'is_resubmission'
    `);
    
    const [batchColumns] = await connection.execute(`
      SHOW COLUMNS FROM voucher_batches LIKE 'contains_resubmissions'
    `);

    if (voucherColumns.length > 0) {
      console.log('✅ is_resubmission field verified in vouchers table');
    } else {
      console.log('❌ is_resubmission field NOT found in vouchers table');
    }

    if (batchColumns.length > 0) {
      console.log('✅ contains_resubmissions field verified in voucher_batches table');
    } else {
      console.log('❌ contains_resubmissions field NOT found in voucher_batches table');
    }

    console.log('🎉 Database migration completed successfully!');

  } catch (error) {
    console.error('❌ Error during migration:', error);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 Database connection closed');
    }
  }
}

// Run the migration
addResubmissionFields();
