/**
 * Department Voucher Tabs Hook - Production Ready
 * 
 * Simple, focused hook for Department Voucher Hubs that:
 * - Uses existing store methods (no workflow complexity)
 * - Manages basic activeTab state
 * - Provides department-specific voucher filtering
 * - Clean separation from workflow system
 */

import { useState, useMemo, useEffect } from 'react';
import { useAppStore } from '@/lib/store';
import { Department, Voucher } from '@/lib/types';

export interface DepartmentVoucherTabsResult {
  // Tab state management
  activeTab: string;
  setActiveTab: (tab: string) => void;
  
  // Department-specific voucher lists
  newVouchers: Voucher[];
  pendingDispatchVouchers: Voucher[];
  dispatchedVouchers: Voucher[];
  returnedVouchers: Voucher[];
  rejectedVouchers: Voucher[];
}

export function useDepartmentVoucherTabs(department: Department): DepartmentVoucherTabsResult {
  // Simple tab state management - starts with 'new-vouchers'
  const [activeTab, setActiveTab] = useState<string>('new-vouchers');

  // Get vouchers from store using existing methods
  const vouchers = useAppStore((state) => state.vouchers);
  const fetchVouchers = useAppStore((state) => state.fetchVouchers);

  // CRITICAL FIX: Fetch vouchers when component mounts
  useEffect(() => {
    console.log(`🔄 Department Voucher Hub (${department}): Component mounted, fetching vouchers`);
    console.log(`🔄 Department Voucher Hub (${department}): Current voucher count in store:`, vouchers.length);

    // Fetch ALL vouchers so we can filter by originalDepartment
    fetchVouchers('ALL').then(() => {
      console.log(`✅ Department Voucher Hub (${department}): Vouchers fetched successfully`);
      const updatedVouchers = useAppStore.getState().vouchers;
      console.log(`✅ Department Voucher Hub (${department}): Updated voucher count:`, updatedVouchers.length);

      // Debug: Show vouchers that match this department
      const matchingVouchers = updatedVouchers.filter(v => v.originalDepartment === department);
      console.log(`🔍 Department Voucher Hub (${department}): Found ${matchingVouchers.length} vouchers with originalDepartment = ${department}`);
      matchingVouchers.forEach(v => {
        console.log(`  - ${v.voucherId}: dept=${v.department}, orig=${v.originalDepartment}, status=${v.status}`);
      });
    }).catch((error) => {
      console.error(`❌ Department Voucher Hub (${department}): Error fetching vouchers:`, error);
    });
  }, [department, fetchVouchers]);
  
  // Filter vouchers by department and status using existing logic
  const departmentVouchers = useMemo(() => {
    return vouchers.filter(voucher => {
      // CRITICAL FIX: Department Voucher Hubs show vouchers that ORIGINATED from that department
      // These vouchers are currently in AUDIT but originally came from the specified department
      if (voucher.originalDepartment !== department) return false;

      // Include vouchers that are currently being processed by Audit for this department
      // These are vouchers that originated from this department and are now in Audit workflow
      return true;
    });
  }, [vouchers, department]);
  
  // Categorize vouchers into department tabs using existing business logic
  const categorizedVouchers = useMemo(() => {
    const newVouchers: Voucher[] = [];
    const pendingDispatchVouchers: Voucher[] = [];
    const dispatchedVouchers: Voucher[] = [];
    const returnedVouchers: Voucher[] = [];
    const rejectedVouchers: Voucher[] = [];
    
    departmentVouchers.forEach(voucher => {
      // NEW VOUCHERS: Vouchers from this department that are new to Audit (ready to work on)
      if (voucher.originalDepartment === department &&
          voucher.department === 'AUDIT' &&
          voucher.status === 'AUDIT: PROCESSING' &&
          voucher.sentToAudit === true &&
          voucher.workStarted !== true) {
        newVouchers.push(voucher);
      }
      // PENDING DISPATCH: Vouchers processed by Audit and ready to send back to department
      else if (voucher.originalDepartment === department &&
               voucher.department === 'AUDIT' &&
               voucher.status === 'AUDIT: PROCESSING' &&
               voucher.sentToAudit === true &&
               voucher.workStarted === true &&
               !voucher.auditDispatchedBy) {
        pendingDispatchVouchers.push(voucher);
      }
      // DISPATCHED: Vouchers sent back to department (but not rejected)
      else if (voucher.originalDepartment === department &&
               voucher.auditDispatchedBy &&
               voucher.status !== 'VOUCHER REJECTED' &&
               voucher.status !== 'VOUCHER RETURNED') {
        dispatchedVouchers.push(voucher);
      }
      // RETURNED: Vouchers returned to department for corrections
      else if (voucher.originalDepartment === department &&
               voucher.status === 'VOUCHER RETURNED') {
        returnedVouchers.push(voucher);
      }
      // REJECTED: Only COPY vouchers rejected by Audit (permanent records)
      else if (voucher.originalDepartment === department &&
               voucher.status === 'VOUCHER REJECTED' &&
               voucher.isRejectionCopy === true) {
        rejectedVouchers.push(voucher);
      }
      // PENDING DISPATCH: Original rejected vouchers ready to be dispatched back
      else if (voucher.originalDepartment === department &&
               voucher.status === 'VOUCHER REJECTED' &&
               voucher.isRejectionCopy !== true &&
               !voucher.auditDispatchedBy) {
        pendingDispatchVouchers.push(voucher);
      }
    });
    
    return {
      newVouchers,
      pendingDispatchVouchers,
      dispatchedVouchers,
      returnedVouchers,
      rejectedVouchers
    };
  }, [departmentVouchers]);
  
  return {
    activeTab,
    setActiveTab,
    ...categorizedVouchers
  };
}
