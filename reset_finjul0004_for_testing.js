const mysql = require('mysql2/promise');

async function resetFINJUL0004ForTesting() {
  console.log('🔄 RESETTING FINJUL0004 FOR RESUBMISSION FIX TESTING');
  console.log('===================================================');
  
  const connection = await mysql.createConnection({
    host: 'localhost',
    user: 'root',
    password: 'vms@2025@1989',
    database: 'vms_production'
  });
  
  try {
    // Find FINJUL0004
    const [vouchers] = await connection.execute(`
      SELECT id, voucher_id, status, workflow_state, is_resubmitted, certified_by, rejected_by
      FROM vouchers 
      WHERE voucher_id = 'FINJUL0004'
    `);
    
    if (vouchers.length === 0) {
      console.log('❌ FINJUL0004 not found');
      return;
    }
    
    const voucher = vouchers[0];
    console.log('📋 CURRENT STATE:');
    console.log(`   Status: ${voucher.status}`);
    console.log(`   Workflow State: ${voucher.workflow_state}`);
    console.log(`   Is Resubmitted: ${voucher.is_resubmitted}`);
    console.log(`   Certified By: ${voucher.certified_by}`);
    console.log(`   Rejected By: ${voucher.rejected_by}`);
    
    console.log('\n🔄 RESETTING TO AUDIT PENDING DISPATCH STATE...');
    
    // Reset the voucher to the state just before dispatch from Audit
    // This simulates the voucher being ready to be dispatched from Audit again
    await connection.execute(`
      UPDATE vouchers
      SET
        status = 'AUDIT: PROCESSING',
        workflow_state = 'AUDIT_PENDING_DISPATCH',
        department = 'AUDIT',
        is_resubmitted = 1,
        batch_id = NULL
      WHERE id = ?
    `, [voucher.id]);
    
    console.log('✅ FINJUL0004 reset successfully!');
    
    // Verify the reset
    const [resetVouchers] = await connection.execute(`
      SELECT id, voucher_id, status, workflow_state, is_resubmitted,
             certified_by, rejected_by, department
      FROM vouchers
      WHERE id = ?
    `, [voucher.id]);
    
    const reset = resetVouchers[0];
    console.log('\n📊 AFTER RESET:');
    console.log(`   Status: ${reset.status}`);
    console.log(`   Workflow State: ${reset.workflow_state}`);
    console.log(`   Department: ${reset.department}`);
    console.log(`   Is Resubmitted: ${reset.is_resubmitted} ✅`);
    console.log(`   Certified By: ${reset.certified_by} ✅`);
    console.log(`   Rejected By: ${reset.rejected_by} (preserved for audit trail)`);
    console.log(`   Dispatched: ${reset.dispatched || 0}`);
    
    console.log('\n🎯 NOW YOU CAN TEST THE FIX:');
    console.log('1. Switch to Audit user (William Akuamoah)');
    console.log('2. Go to Audit PENDING DISPATCH tab');
    console.log('3. Dispatch FINJUL0004 back to Finance');
    console.log('4. Switch to Finance user');
    console.log('5. Receive the batch - it should now work correctly!');
    console.log('');
    console.log('✅ EXPECTED RESULTS WITH THE FIX:');
    console.log('   - Status: VOUCHER CERTIFIED (not REJECTED)');
    console.log('   - Workflow State: AUDIT_DISPATCHED (not FINANCE_REJECTED)');
    console.log('   - Finance Received: 1');
    console.log('   - Appears in Finance CERTIFIED tab');
    console.log('   - Remains in Audit DISPATCHED tab');
    console.log('   - Shows CERTIFIED-RESUBMISSION badge');
    
  } catch (error) {
    console.error('❌ Error resetting FINJUL0004:', error);
  } finally {
    await connection.end();
  }
}

resetFINJUL0004ForTesting().catch(console.error);
