const mysql = require('mysql2/promise');
const { v4: uuidv4 } = require('uuid');

async function testBadgeLogic() {
  console.log('🏷️ TESTING BADGE LOGIC THROUGHOUT WORKFLOW');
  console.log('==========================================');
  
  const connection = await mysql.createConnection({
    host: 'localhost',
    user: 'root',
    password: 'vms@2025@1989',
    database: 'vms_production'
  });
  
  try {
    // Test 1: Normal Voucher (No Badge)
    console.log('\n📋 TEST 1: NORMAL VOUCHER (NO BADGE)');
    console.log('====================================');
    
    const normalId = uuidv4();
    const normalCode = 'NORMAL_BADGE_' + Date.now();
    
    await connection.execute(`
      INSERT INTO vouchers (
        id, voucher_id, date, claimant, description, amount, currency,
        department, original_department, status, workflow_state,
        created_by, created_at, is_resubmitted
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), ?)
    `, [
      normalId, normalCode, '2025-01-21', 'Normal Claimant', 'Normal voucher',
      1000.00, 'GHS', 'FINANCE', 'FINANCE', 'PENDING', 'FINANCE_PENDING',
      'TEST USER', 0
    ]);
    
    const [normalResult] = await connection.execute(`
      SELECT voucher_id, is_resubmitted, certified_by, rejected_by
      FROM vouchers WHERE id = ?
    `, [normalId]);
    
    const normal = normalResult[0];
    const normalBadge = getBadgeType(normal);
    
    console.log('📊 Normal Voucher:');
    console.log('   voucher_id:', normal.voucher_id);
    console.log('   is_resubmitted:', normal.is_resubmitted);
    console.log('   certified_by:', normal.certified_by);
    console.log('   rejected_by:', normal.rejected_by);
    console.log('   Expected Badge: NONE');
    console.log('   Actual Badge:', normalBadge);
    console.log('   ✅ Badge Logic:', normalBadge === 'NONE' ? 'CORRECT' : 'INCORRECT');
    
    // Test 2: Resubmission Voucher (RESUBMISSION Badge)
    console.log('\n📋 TEST 2: RESUBMISSION VOUCHER (RESUBMISSION BADGE)');
    console.log('===================================================');
    
    const resubId = uuidv4();
    const resubCode = 'RESUB_BADGE_' + Date.now();
    
    await connection.execute(`
      INSERT INTO vouchers (
        id, voucher_id, date, claimant, description, amount, currency,
        department, original_department, status, workflow_state,
        created_by, created_at, is_resubmitted, rejected_by, rejection_time, comment
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), ?, ?, NOW(), ?)
    `, [
      resubId, resubCode, '2025-01-21', 'Resubmission Claimant', 'Resubmission voucher',
      1500.00, 'GHS', 'AUDIT', 'FINANCE', 'VOUCHER PROCESSING', 'AUDIT_NEW_RESUBMITTED',
      'TEST USER', 1, 'AUDIT USER', 'Missing documents'
    ]);
    
    const [resubResult] = await connection.execute(`
      SELECT voucher_id, is_resubmitted, certified_by, rejected_by
      FROM vouchers WHERE id = ?
    `, [resubId]);
    
    const resub = resubResult[0];
    const resubBadge = getBadgeType(resub);
    
    console.log('📊 Resubmission Voucher:');
    console.log('   voucher_id:', resub.voucher_id);
    console.log('   is_resubmitted:', resub.is_resubmitted);
    console.log('   certified_by:', resub.certified_by);
    console.log('   rejected_by:', resub.rejected_by);
    console.log('   Expected Badge: RESUBMISSION');
    console.log('   Actual Badge:', resubBadge);
    console.log('   ✅ Badge Logic:', resubBadge === 'RESUBMISSION' ? 'CORRECT' : 'INCORRECT');
    
    // Test 3: Certified Resubmission Voucher (CERTIFIED-RESUBMISSION Badge)
    console.log('\n📋 TEST 3: CERTIFIED RESUBMISSION (CERTIFIED-RESUBMISSION BADGE)');
    console.log('===============================================================');
    
    const certResubId = uuidv4();
    const certResubCode = 'CERT_RESUB_BADGE_' + Date.now();
    
    await connection.execute(`
      INSERT INTO vouchers (
        id, voucher_id, date, claimant, description, amount, currency,
        department, original_department, status, workflow_state,
        created_by, created_at, is_resubmitted, rejected_by, rejection_time, 
        comment, certified_by
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), ?, ?, NOW(), ?, ?)
    `, [
      certResubId, certResubCode, '2025-01-21', 'Certified Resubmission Claimant', 'Certified resubmission voucher',
      2000.00, 'GHS', 'FINANCE', 'FINANCE', 'VOUCHER CERTIFIED', 'FINANCE_CERTIFIED',
      'TEST USER', 1, 'AUDIT USER', 'Missing documents - now provided', 'FINANCE USER'
    ]);
    
    const [certResubResult] = await connection.execute(`
      SELECT voucher_id, is_resubmitted, certified_by, rejected_by
      FROM vouchers WHERE id = ?
    `, [certResubId]);
    
    const certResub = certResubResult[0];
    const certResubBadge = getBadgeType(certResub);
    
    console.log('📊 Certified Resubmission Voucher:');
    console.log('   voucher_id:', certResub.voucher_id);
    console.log('   is_resubmitted:', certResub.is_resubmitted);
    console.log('   certified_by:', certResub.certified_by);
    console.log('   rejected_by:', certResub.rejected_by);
    console.log('   Expected Badge: CERTIFIED-RESUBMISSION');
    console.log('   Actual Badge:', certResubBadge);
    console.log('   ✅ Badge Logic:', certResubBadge === 'CERTIFIED-RESUBMISSION' ? 'CORRECT' : 'INCORRECT');
    
    // Test 4: Rejected Voucher (No Badge - rejection handled by status)
    console.log('\n📋 TEST 4: REJECTED VOUCHER (NO RESUBMISSION BADGE)');
    console.log('==================================================');
    
    const rejectedId = uuidv4();
    const rejectedCode = 'REJECTED_BADGE_' + Date.now();
    
    await connection.execute(`
      INSERT INTO vouchers (
        id, voucher_id, date, claimant, description, amount, currency,
        department, original_department, status, workflow_state,
        created_by, created_at, is_resubmitted, rejected_by, rejection_time, comment
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), ?, ?, NOW(), ?)
    `, [
      rejectedId, rejectedCode, '2025-01-21', 'Rejected Claimant', 'Rejected voucher',
      1200.00, 'GHS', 'FINANCE', 'FINANCE', 'VOUCHER REJECTED', 'FINANCE_REJECTED',
      'TEST USER', 0, 'AUDIT USER', 'Insufficient documentation'
    ]);
    
    const [rejectedResult] = await connection.execute(`
      SELECT voucher_id, is_resubmitted, certified_by, rejected_by
      FROM vouchers WHERE id = ?
    `, [rejectedId]);
    
    const rejected = rejectedResult[0];
    const rejectedBadge = getBadgeType(rejected);
    
    console.log('📊 Rejected Voucher:');
    console.log('   voucher_id:', rejected.voucher_id);
    console.log('   is_resubmitted:', rejected.is_resubmitted);
    console.log('   certified_by:', rejected.certified_by);
    console.log('   rejected_by:', rejected.rejected_by);
    console.log('   Expected Badge: NONE (rejection shown by status)');
    console.log('   Actual Badge:', rejectedBadge);
    console.log('   ✅ Badge Logic:', rejectedBadge === 'NONE' ? 'CORRECT' : 'INCORRECT');
    
    // Summary
    console.log('\n🎯 BADGE LOGIC TEST SUMMARY:');
    console.log('============================');
    const allCorrect = normalBadge === 'NONE' && 
                      resubBadge === 'RESUBMISSION' && 
                      certResubBadge === 'CERTIFIED-RESUBMISSION' && 
                      rejectedBadge === 'NONE';
    
    console.log('✅ Normal Voucher:', normalBadge === 'NONE' ? 'PASS' : 'FAIL');
    console.log('✅ Resubmission Voucher:', resubBadge === 'RESUBMISSION' ? 'PASS' : 'FAIL');
    console.log('✅ Certified Resubmission:', certResubBadge === 'CERTIFIED-RESUBMISSION' ? 'PASS' : 'FAIL');
    console.log('✅ Rejected Voucher:', rejectedBadge === 'NONE' ? 'PASS' : 'FAIL');
    
    if (allCorrect) {
      console.log('\n🎉 ALL BADGE LOGIC TESTS PASSED!');
      console.log('✅ Badge logic is consistent and correct');
      console.log('✅ Resubmissions show proper badges throughout workflow');
      console.log('✅ Certified resubmissions show enhanced badges');
      console.log('✅ Normal and rejected vouchers show no resubmission badges');
    } else {
      console.log('\n❌ SOME BADGE LOGIC TESTS FAILED!');
    }
    
    // Clean up test vouchers
    await connection.execute('DELETE FROM vouchers WHERE id IN (?, ?, ?, ?)', 
      [normalId, resubId, certResubId, rejectedId]);
    console.log('\n🧹 Test vouchers cleaned up');
    
  } catch (error) {
    console.error('❌ Error testing badge logic:', error);
  } finally {
    await connection.end();
  }
}

// Badge logic function (matches client-side logic)
function getBadgeType(voucher) {
  const isResubmission = voucher.is_resubmitted === 1 || voucher.is_resubmitted === true;
  
  if (!isResubmission) {
    return 'NONE';
  }
  
  const isCertified = voucher.certified_by && voucher.certified_by.trim() !== '';
  
  if (isCertified) {
    return 'CERTIFIED-RESUBMISSION';
  } else {
    return 'RESUBMISSION';
  }
}

testBadgeLogic().catch(console.error);
