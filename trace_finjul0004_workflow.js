const mysql = require('mysql2/promise');

async function traceFINJUL0004Workflow() {
  console.log('🔍 TRACING FINJUL0004 RESUBMISSION WORKFLOW STEP BY STEP');
  console.log('========================================================');
  
  const connection = await mysql.createConnection({
    host: 'localhost',
    user: 'root',
    password: 'vms@2025@1989',
    database: 'vms_production'
  });
  
  let stepCounter = 1;
  
  async function logCurrentState(stepDescription) {
    console.log(`\n🎯 STEP ${stepCounter}: ${stepDescription}`);
    console.log('='.repeat(50 + stepDescription.length));
    
    try {
      // Find FINJUL0004 voucher(s)
      const [vouchers] = await connection.execute(`
        SELECT id, voucher_id, status, workflow_state, department, original_department,
               is_resubmitted, rejected_by, rejection_time, comment, certified_by,
               received_by, department_received_by, finance_received, dispatched_by,
               audit_dispatched_by, batch_id, created_at, work_started,
               sent_to_audit, dispatch_to_audit_by, dispatch_time
        FROM vouchers 
        WHERE voucher_id LIKE '%FINJUL0004%' OR voucher_id = 'FINJUL0004'
        ORDER BY created_at DESC
      `);
      
      if (vouchers.length === 0) {
        console.log('❌ No FINJUL0004 voucher found yet');
        return null;
      }
      
      console.log(`📊 Found ${vouchers.length} FINJUL0004 voucher(s):`);
      
      vouchers.forEach((voucher, index) => {
        console.log(`\n📋 VOUCHER ${index + 1}: ${voucher.voucher_id}`);
        console.log('   ├─ id:', voucher.id);
        console.log('   ├─ status:', voucher.status);
        console.log('   ├─ workflow_state:', voucher.workflow_state);
        console.log('   ├─ department:', voucher.department);
        console.log('   ├─ original_department:', voucher.original_department);
        console.log('   ├─ is_resubmitted:', voucher.is_resubmitted);
        console.log('   ├─ work_started:', voucher.work_started);
        console.log('   ├─ sent_to_audit:', voucher.sent_to_audit);
        console.log('   ├─ dispatch_to_audit_by:', voucher.dispatch_to_audit_by);
        console.log('   ├─ dispatch_time:', voucher.dispatch_time);
        console.log('   ├─ rejected_by:', voucher.rejected_by);
        console.log('   ├─ rejection_time:', voucher.rejection_time);
        console.log('   ├─ comment:', voucher.comment);
        console.log('   ├─ certified_by:', voucher.certified_by);
        console.log('   ├─ received_by:', voucher.received_by);
        console.log('   ├─ department_received_by:', voucher.department_received_by);
        console.log('   ├─ finance_received:', voucher.finance_received);
        console.log('   ├─ dispatched_by:', voucher.dispatched_by);
        console.log('   ├─ audit_dispatched_by:', voucher.audit_dispatched_by);
        console.log('   ├─ batch_id:', voucher.batch_id);
        console.log('   └─ created_at:', voucher.created_at);
        
        // Analyze current state
        console.log('\n🔍 STATE ANALYSIS:');
        
        if (voucher.is_resubmitted === 1) {
          console.log('   ✅ This IS a resubmission voucher');
          
          if (voucher.status === 'VOUCHER CERTIFIED' && voucher.workflow_state === 'AUDIT_DISPATCHED' && voucher.finance_received === 1) {
            console.log('   🎯 SHOULD appear in Finance CERTIFIED tab');
          } else if (voucher.status === 'VOUCHER CERTIFIED' && voucher.workflow_state === 'AUDIT_DISPATCHED') {
            console.log('   🎯 SHOULD appear in Audit DISPATCHED tab');
          } else if (voucher.status === 'PENDING' && voucher.workflow_state === 'FINANCE_PENDING') {
            console.log('   🎯 SHOULD appear in Finance PENDING tab with RESUBMISSION badge');
          } else if (voucher.status === 'VOUCHER PROCESSING' && voucher.workflow_state === 'AUDIT_PROCESSING') {
            console.log('   🎯 SHOULD appear in Audit NEW VOUCHERS tab with RESUBMISSION badge');
          }
          
          // Badge logic
          const isCertified = voucher.certified_by || voucher.department_received_by;
          const expectedBadge = isCertified ? 'CERTIFIED-RESUBMISSION' : 'RESUBMISSION';
          console.log(`   🏷️ Expected Badge: ${expectedBadge}`);
          
        } else {
          console.log('   ❌ This is NOT a resubmission voucher');
          
          if (voucher.status === 'VOUCHER REJECTED' && voucher.workflow_state === 'FINANCE_REJECTED') {
            console.log('   🎯 SHOULD appear in Finance REJECTED tab');
            console.log('   💡 Can use "Add Back" button to create resubmission');
          }
        }
      });
      
      // Check related batches
      const voucherIds = vouchers.map(v => v.id);
      if (voucherIds.length > 0) {
        const [batches] = await connection.execute(`
          SELECT vb.id as batch_id, vb.department, vb.sent_by, vb.sent_time, 
                 vb.received, vb.from_audit, bv.voucher_id
          FROM voucher_batches vb
          JOIN batch_vouchers bv ON vb.id = bv.batch_id
          WHERE bv.voucher_id IN (${voucherIds.map(() => '?').join(',')})
          ORDER BY vb.sent_time DESC
          LIMIT 5
        `, voucherIds);
        
        if (batches.length > 0) {
          console.log(`\n📦 RELATED BATCHES (${batches.length}):`);
          batches.forEach((batch, i) => {
            console.log(`   ${i+1}. Batch ${batch.batch_id}:`);
            console.log(`      ├─ department: ${batch.department}`);
            console.log(`      ├─ sent_by: ${batch.sent_by}`);
            console.log(`      ├─ received: ${batch.received}`);
            console.log(`      ├─ from_audit: ${batch.from_audit}`);
            console.log(`      └─ sent_time: ${batch.sent_time}`);
          });
        }
      }
      
      stepCounter++;
      return vouchers;
      
    } catch (error) {
      console.error('❌ Error tracing workflow:', error);
      return null;
    }
  }
  
  try {
    // Initial state check
    await logCurrentState('INITIAL STATE CHECK');
    
    console.log('\n🎯 WORKFLOW MONITORING ACTIVE');
    console.log('=============================');
    console.log('Now perform your resubmission workflow actions...');
    console.log('I will check for changes every 5 seconds.');
    console.log('Press Ctrl+C to stop monitoring.');
    
    let previousState = null;
    
    // Monitor for changes every 5 seconds
    const monitorInterval = setInterval(async () => {
      try {
        const [currentVouchers] = await connection.execute(`
          SELECT id, voucher_id, status, workflow_state, department, is_resubmitted,
                 finance_received, batch_id, received_by, department_received_by,
                 audit_dispatched_by, certified_by, rejected_by
          FROM vouchers 
          WHERE voucher_id LIKE '%FINJUL0004%' OR voucher_id = 'FINJUL0004'
          ORDER BY created_at DESC
        `);
        
        if (currentVouchers.length > 0) {
          const currentState = JSON.stringify(currentVouchers);
          
          if (previousState && currentState !== previousState) {
            console.log('\n🔄 CHANGE DETECTED!');
            console.log('==================');
            await logCurrentState('STATE CHANGE DETECTED');
          }
          
          previousState = currentState;
        }
        
      } catch (error) {
        console.error('❌ Error monitoring changes:', error);
      }
    }, 5000);
    
    // Keep monitoring until interrupted
    process.on('SIGINT', async () => {
      console.log('\n\n🛑 MONITORING STOPPED');
      console.log('====================');
      clearInterval(monitorInterval);
      
      // Final state check
      await logCurrentState('FINAL STATE CHECK');
      
      await connection.end();
      process.exit(0);
    });
    
  } catch (error) {
    console.error('❌ Error in workflow tracing:', error);
  }
}

// Start tracing
traceFINJUL0004Workflow().catch(console.error);

// Keep the process alive
process.stdin.resume();
