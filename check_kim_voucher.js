const mysql = require('mysql2/promise');
require('dotenv').config();

async function checkKimVoucher() {
  let connection;
  
  try {
    // Create connection
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || 'vms@2025@1989',
      database: process.env.DB_NAME || 'vms_production'
    });

    console.log('🔗 Connected to database');
    console.log('🔍 Checking voucher with claimant KIM...\n');

    // Find vouchers with claimant containing KIM
    const [results] = await connection.execute(`
      SELECT
        voucher_id,
        claimant,
        status,
        workflow_state,
        is_resubmission,
        sent_to_audit,
        comment,
        rejected_by,
        rejection_time,
        date
      FROM vouchers
      WHERE claimant LIKE '%KIM%'
      ORDER BY date DESC
    `);

    if (results.length === 0) {
      console.log('❌ No vouchers found with claimant containing "KIM"');
      return;
    }

    console.log(`📊 Found ${results.length} voucher(s) with claimant KIM:\n`);

    results.forEach((voucher, index) => {
      console.log(`--- Voucher ${index + 1} ---`);
      console.log(`Voucher ID: ${voucher.voucher_id}`);
      console.log(`Claimant: ${voucher.claimant}`);
      console.log(`Status: ${voucher.status}`);
      console.log(`Workflow State: ${voucher.workflow_state || 'NULL'}`);
      console.log(`Is Resubmission: ${voucher.is_resubmission ? 'YES' : 'NO'}`);
      console.log(`Sent to Audit: ${voucher.sent_to_audit ? 'YES' : 'NO'}`);
      console.log(`Comment: ${voucher.comment || 'None'}`);
      console.log(`Rejected By: ${voucher.rejected_by || 'None'}`);
      console.log(`Rejection Time: ${voucher.rejection_time || 'None'}`);
      console.log(`Date: ${voucher.date}`);
      console.log('');
    });

    // Analyze the issue
    const kimVoucher = results[0]; // Most recent
    console.log('🔍 ANALYSIS:');
    
    if (kimVoucher.status === 'PENDING SUBMISSION') {
      console.log('✅ Status is correctly "PENDING SUBMISSION" after Add Back');
      console.log('   This is the expected behavior when a voucher is added back to pending.');
      console.log('   The voucher should show RE-SUBMISSION status only AFTER it\'s sent to Audit again.');
      
      if (kimVoucher.comment && kimVoucher.rejected_by) {
        console.log('✅ Original rejection information is preserved:');
        console.log(`   - Rejection reason: ${kimVoucher.comment}`);
        console.log(`   - Rejected by: ${kimVoucher.rejected_by}`);
      }
      
      console.log('\n🎯 NEXT STEPS:');
      console.log('1. Select the voucher in Finance PENDING tab');
      console.log('2. Choose a dispatcher');
      console.log('3. Click "Send to Audit"');
      console.log('4. Status should change to "RE-SUBMISSION"');
      console.log('5. is_resubmission flag should be set to true');
      
    } else if (kimVoucher.status === 'RE-SUBMISSION') {
      console.log('✅ Status is "RE-SUBMISSION" - voucher has been resubmitted');
      console.log(`✅ Is Resubmission flag: ${kimVoucher.is_resubmission ? 'Correctly set' : '❌ NOT SET'}`);
      
    } else {
      console.log(`❓ Unexpected status: ${kimVoucher.status}`);
      console.log('   Expected: PENDING SUBMISSION (after Add Back) or RE-SUBMISSION (after Send to Audit)');
    }

  } catch (error) {
    console.error('❌ Error checking KIM voucher:', error);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n🔌 Database connection closed');
    }
  }
}

// Run the check
checkKimVoucher();
