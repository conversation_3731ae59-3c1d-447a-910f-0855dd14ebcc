{"version": 3, "file": "legacy-voucher-status-compat.js", "sourceRoot": "", "sources": ["../../src/utils/legacy-voucher-status-compat.ts"], "names": [], "mappings": ";AAAA;;;;GAIG;;;AAkBH,0DAeC;AAGD,0DA6BC;AAGD,kEAQC;AAGD,wEAcC;AAGD,wEAmBC;AAGD,oEA8BC;AAGD,0CAIC;AAzJD,yFAAwE;AAExE,qDAAqD;AACxC,QAAA,gBAAgB,GAAG;IAC9B,OAAO,EAAE,oBAA6B;IACtC,eAAe,EAAE,iBAA0B;IAC3C,kBAAkB,EAAE,oBAA6B;IACjD,gBAAgB,EAAE,mBAA4B;IAC9C,iBAAiB,EAAE,mBAA4B;IAC/C,gBAAgB,EAAE,kBAA2B;IAC7C,gBAAgB,EAAE,kBAA2B;IAC7C,gBAAgB,EAAE,kBAA2B;IAC7C,UAAU,EAAE,YAAqB;CAClC,CAAC;AAEF,2CAA2C;AAC3C,SAAgB,uBAAuB,CAAC,OAA2B;IACjE,MAAM,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;IAE3B,sCAAsC;IACtC,MAAM,KAAK,GAAG;QACZ,KAAK,EAAE,MAAM,KAAK,wBAAgB,CAAC,OAAO;QAC1C,iBAAiB,EAAE,MAAM,KAAK,wBAAgB,CAAC,gBAAgB;QAC/D,YAAY,EAAE,MAAM,KAAK,wBAAgB,CAAC,UAAU;QACpD,WAAW,EAAE,MAAM,KAAK,wBAAgB,CAAC,iBAAiB;QAC1D,UAAU,EAAE,MAAM,KAAK,wBAAgB,CAAC,gBAAgB;QACxD,UAAU,EAAE,MAAM,KAAK,wBAAgB,CAAC,gBAAgB;QACxD,YAAY,EAAE,MAAM,KAAK,wBAAgB,CAAC,kBAAkB,IAAI,MAAM,KAAK,wBAAgB,CAAC,gBAAgB;KAC7G,CAAC;IAEF,OAAO,EAAE,KAAK,EAAE,CAAC;AACnB,CAAC;AAED,mDAAmD;AACnD,SAAgB,uBAAuB,CACrC,UAAkB,EAClB,QAAgB,EAChB,QAAiB,EACjB,OAAa;IAGb,kDAAkD;IAClD,iEAAiE;IACjE,OAAO,CAAC,IAAI,CAAC,mFAAmF,CAAC,CAAC;IAElG,yBAAyB;IACzB,MAAM,gBAAgB,GAA6B;QACjD,CAAC,wBAAgB,CAAC,OAAO,CAAC,EAAE,CAAC,wBAAgB,CAAC,eAAe,CAAC;QAC9D,CAAC,wBAAgB,CAAC,eAAe,CAAC,EAAE,CAAC,wBAAgB,CAAC,kBAAkB,EAAE,wBAAgB,CAAC,gBAAgB,CAAC;QAC5G,CAAC,wBAAgB,CAAC,kBAAkB,CAAC,EAAE,CAAC,wBAAgB,CAAC,iBAAiB,EAAE,wBAAgB,CAAC,gBAAgB,EAAE,wBAAgB,CAAC,gBAAgB,CAAC;QACjJ,CAAC,wBAAgB,CAAC,gBAAgB,CAAC,EAAE,CAAC,wBAAgB,CAAC,gBAAgB,CAAC;QACxE,CAAC,wBAAgB,CAAC,gBAAgB,CAAC,EAAE,CAAC,wBAAgB,CAAC,UAAU,CAAC;QAClE,CAAC,wBAAgB,CAAC,gBAAgB,CAAC,EAAE,CAAC,wBAAgB,CAAC,kBAAkB,CAAC;QAC1E,CAAC,wBAAgB,CAAC,gBAAgB,CAAC,EAAE,CAAC,wBAAgB,CAAC,kBAAkB,CAAC;KAC3E,CAAC;IAEF,MAAM,kBAAkB,GAAG,gBAAgB,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC;IAC9D,MAAM,OAAO,GAAG,kBAAkB,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;IAEtD,OAAO;QACL,OAAO;QACP,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,mBAAmB,UAAU,OAAO,QAAQ,cAAc;KACzF,CAAC;AACJ,CAAC;AAED,+DAA+D;AAC/D,SAAgB,2BAA2B,CACzC,UAAkB,EAClB,QAAgB,EAChB,QAAiB,EACjB,OAAa;IAEb,MAAM,MAAM,GAAG,uBAAuB,CAAC,UAAU,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;IAChF,OAAO,MAAM,CAAC,OAAO,CAAC;AACxB,CAAC;AAED,sCAAsC;AACtC,SAAgB,8BAA8B,CAAC,YAAoB;IACjE,MAAM,OAAO,GAAkC;QAC7C,CAAC,wBAAgB,CAAC,OAAO,CAAC,EAAE,2CAAa,CAAC,eAAe;QACzD,CAAC,wBAAgB,CAAC,eAAe,CAAC,EAAE,2CAAa,CAAC,kBAAkB;QACpE,CAAC,wBAAgB,CAAC,kBAAkB,CAAC,EAAE,2CAAa,CAAC,SAAS;QAC9D,CAAC,wBAAgB,CAAC,gBAAgB,CAAC,EAAE,2CAAa,CAAC,sBAAsB;QACzE,CAAC,wBAAgB,CAAC,gBAAgB,CAAC,EAAE,2CAAa,CAAC,sBAAsB;QACzE,CAAC,wBAAgB,CAAC,UAAU,CAAC,EAAE,2CAAa,CAAC,gBAAgB;QAC7D,CAAC,wBAAgB,CAAC,iBAAiB,CAAC,EAAE,2CAAa,CAAC,iBAAiB;QACrE,CAAC,wBAAgB,CAAC,gBAAgB,CAAC,EAAE,2CAAa,CAAC,gBAAgB;QACnE,CAAC,wBAAgB,CAAC,gBAAgB,CAAC,EAAE,2CAAa,CAAC,gBAAgB;KACpE,CAAC;IAEF,OAAO,OAAO,CAAC,YAAY,CAAC,IAAI,2CAAa,CAAC,eAAe,CAAC;AAChE,CAAC;AAED,sCAAsC;AACtC,SAAgB,8BAA8B,CAAC,aAA4B;IACzE,MAAM,OAAO,GAAkC;QAC7C,CAAC,2CAAa,CAAC,eAAe,CAAC,EAAE,wBAAgB,CAAC,OAAO;QACzD,CAAC,2CAAa,CAAC,kBAAkB,CAAC,EAAE,wBAAgB,CAAC,eAAe;QACpE,CAAC,2CAAa,CAAC,iBAAiB,CAAC,EAAE,wBAAgB,CAAC,iBAAiB;QACrE,CAAC,2CAAa,CAAC,gBAAgB,CAAC,EAAE,wBAAgB,CAAC,gBAAgB;QACnE,CAAC,2CAAa,CAAC,gBAAgB,CAAC,EAAE,wBAAgB,CAAC,gBAAgB;QACnE,CAAC,2CAAa,CAAC,6BAA6B,CAAC,EAAE,eAAe;QAC9D,CAAC,2CAAa,CAAC,SAAS,CAAC,EAAE,wBAAgB,CAAC,kBAAkB;QAC9D,CAAC,2CAAa,CAAC,qBAAqB,CAAC,EAAE,wBAAgB,CAAC,kBAAkB;QAC1E,CAAC,2CAAa,CAAC,sBAAsB,CAAC,EAAE,wBAAgB,CAAC,gBAAgB;QACzE,CAAC,2CAAa,CAAC,+BAA+B,CAAC,EAAE,wBAAgB,CAAC,gBAAgB;QAClF,CAAC,2CAAa,CAAC,+BAA+B,CAAC,EAAE,wBAAgB,CAAC,gBAAgB;QAClF,CAAC,2CAAa,CAAC,gBAAgB,CAAC,EAAE,wBAAgB,CAAC,UAAU;QAC7D,CAAC,2CAAa,CAAC,mBAAmB,CAAC,EAAE,wBAAgB,CAAC,gBAAgB;QACtE,CAAC,2CAAa,CAAC,mBAAmB,CAAC,EAAE,wBAAgB,CAAC,gBAAgB;KACvE,CAAC;IAEF,OAAO,OAAO,CAAC,aAAa,CAAC,IAAI,wBAAgB,CAAC,OAAO,CAAC;AAC5D,CAAC;AAED,8EAA8E;AACvE,KAAK,UAAU,4BAA4B,CAChD,EAAO,EACP,SAAiB,EACjB,aAA4B,EAC5B,mBAAwC,EAAE;IAE1C,MAAM,YAAY,GAAG,8BAA8B,CAAC,aAAa,CAAC,CAAC;IACnE,MAAM,EAAE,KAAK,EAAE,GAAG,uBAAuB,CAAC,EAAE,MAAM,EAAE,YAAY,EAAE,CAAC,CAAC;IAEpE,MAAM,YAAY,GAAG;QACnB,MAAM,EAAE,YAAY;QACpB,cAAc,EAAE,aAAa;QAC7B,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC;QAC5B,aAAa,EAAE,IAAI,IAAI,EAAE;QACzB,GAAG,gBAAgB;KACpB,CAAC;IAEF,MAAM,SAAS,GAAG,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC;SACxC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,MAAM,CAAC;SACxB,IAAI,CAAC,IAAI,CAAC,CAAC;IAEd,MAAM,MAAM,GAAG,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,EAAE,SAAS,CAAC,CAAC;IAE3D,MAAM,EAAE,CAAC,OAAO,CAAC;;UAET,SAAS;;GAEhB,EAAE,MAAM,CAAC,CAAC;IAEX,OAAO,CAAC,GAAG,CAAC,qBAAqB,SAAS,KAAK,YAAY,KAAK,aAAa,GAAG,CAAC,CAAC;AACpF,CAAC;AAED,uBAAuB;AACvB,SAAgB,eAAe,CAAC,YAAoB,EAAE,QAAgB;IACpE,OAAO,CAAC,IAAI,CAAC,mBAAmB,YAAY,YAAY,QAAQ,EAAE,CAAC,CAAC;IACpE,OAAO,CAAC,IAAI,CAAC,6CAA6C,CAAC,CAAC;IAC5D,OAAO,CAAC,IAAI,CAAC,kDAAkD,CAAC,CAAC;AACnE,CAAC"}