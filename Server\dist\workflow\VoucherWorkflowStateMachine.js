"use strict";
/**
 * VMS Voucher Workflow State Machine
 * Single Source of Truth for all voucher workflow states and transitions
 * Based on END TO END VOUCHER WORKFLOW.txt specification
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.VoucherWorkflowStateMachine = exports.BadgeType = exports.WorkflowEvent = exports.WorkflowState = void 0;
var WorkflowState;
(function (WorkflowState) {
    // Finance States
    WorkflowState["FINANCE_PENDING"] = "FINANCE_PENDING";
    WorkflowState["FINANCE_PROCESSING"] = "FINANCE_PROCESSING";
    WorkflowState["FINANCE_CERTIFIED"] = "FINANCE_CERTIFIED";
    WorkflowState["FINANCE_REJECTED"] = "FINANCE_REJECTED";
    WorkflowState["FINANCE_RETURNED"] = "FINANCE_RETURNED";
    WorkflowState["FINANCE_RESUBMISSION_RECEIVED"] = "FINANCE_RESUBMISSION_RECEIVED";
    // Audit States
    WorkflowState["AUDIT_NEW"] = "AUDIT_NEW";
    WorkflowState["AUDIT_NEW_RESUBMITTED"] = "AUDIT_NEW_RESUBMITTED";
    WorkflowState["AUDIT_PENDING_DISPATCH"] = "AUDIT_PENDING_DISPATCH";
    WorkflowState["AUDIT_PENDING_DISPATCH_REJECTED"] = "AUDIT_PENDING_DISPATCH_REJECTED";
    WorkflowState["AUDIT_PENDING_DISPATCH_RETURNED"] = "AUDIT_PENDING_DISPATCH_RETURNED";
    WorkflowState["AUDIT_DISPATCHED"] = "AUDIT_DISPATCHED";
    WorkflowState["AUDIT_REJECTED_COPY"] = "AUDIT_REJECTED_COPY";
    WorkflowState["AUDIT_RETURNED_COPY"] = "AUDIT_RETURNED_COPY";
})(WorkflowState || (exports.WorkflowState = WorkflowState = {}));
var WorkflowEvent;
(function (WorkflowEvent) {
    // Finance Events
    WorkflowEvent["CREATE_VOUCHER"] = "CREATE_VOUCHER";
    WorkflowEvent["SUBMIT_TO_AUDIT"] = "SUBMIT_TO_AUDIT";
    WorkflowEvent["RESUBMIT_FROM_REJECTED"] = "RESUBMIT_FROM_REJECTED";
    WorkflowEvent["RESUBMIT_FROM_RETURNED"] = "RESUBMIT_FROM_RETURNED";
    // Audit Events
    WorkflowEvent["RECEIVE_FROM_FINANCE"] = "RECEIVE_FROM_FINANCE";
    WorkflowEvent["START_WORK"] = "START_WORK";
    WorkflowEvent["CERTIFY_VOUCHER"] = "CERTIFY_VOUCHER";
    WorkflowEvent["REJECT_VOUCHER"] = "REJECT_VOUCHER";
    WorkflowEvent["RETURN_VOUCHER"] = "RETURN_VOUCHER";
    WorkflowEvent["DISPATCH_TO_FINANCE"] = "DISPATCH_TO_FINANCE";
})(WorkflowEvent || (exports.WorkflowEvent = WorkflowEvent = {}));
var BadgeType;
(function (BadgeType) {
    BadgeType["NONE"] = "NONE";
    BadgeType["RE_SUBMITTED"] = "RE_SUBMITTED";
    BadgeType["RESUBMISSION"] = "RESUBMISSION";
    BadgeType["RETURNED"] = "RETURNED";
    BadgeType["REJECTED"] = "REJECTED";
})(BadgeType || (exports.BadgeType = BadgeType = {}));
class VoucherWorkflowStateMachine {
    static TRANSITIONS = [
        // Normal Flow
        {
            fromState: WorkflowState.FINANCE_PENDING,
            event: WorkflowEvent.SUBMIT_TO_AUDIT,
            toState: WorkflowState.FINANCE_PROCESSING,
            isValid: true
        },
        {
            fromState: WorkflowState.FINANCE_PROCESSING,
            event: WorkflowEvent.RECEIVE_FROM_FINANCE,
            toState: WorkflowState.AUDIT_NEW,
            isValid: true
        },
        {
            fromState: WorkflowState.AUDIT_NEW,
            event: WorkflowEvent.START_WORK,
            toState: WorkflowState.AUDIT_PENDING_DISPATCH,
            isValid: true
        },
        {
            fromState: WorkflowState.AUDIT_PENDING_DISPATCH,
            event: WorkflowEvent.DISPATCH_TO_FINANCE,
            toState: WorkflowState.AUDIT_DISPATCHED,
            isValid: true
        },
        {
            fromState: WorkflowState.AUDIT_DISPATCHED,
            event: WorkflowEvent.RECEIVE_FROM_FINANCE,
            toState: WorkflowState.FINANCE_CERTIFIED,
            isValid: true
        },
        // Rejection Flow
        {
            fromState: WorkflowState.AUDIT_NEW,
            event: WorkflowEvent.REJECT_VOUCHER,
            toState: WorkflowState.AUDIT_PENDING_DISPATCH_REJECTED,
            isValid: true,
            requiresCopy: true,
            copyState: WorkflowState.AUDIT_REJECTED_COPY,
            badge: BadgeType.REJECTED
        },
        {
            fromState: WorkflowState.AUDIT_PENDING_DISPATCH_REJECTED,
            event: WorkflowEvent.DISPATCH_TO_FINANCE,
            toState: WorkflowState.FINANCE_REJECTED,
            isValid: true
        },
        // Resubmission Flow
        {
            fromState: WorkflowState.FINANCE_REJECTED,
            event: WorkflowEvent.RESUBMIT_FROM_REJECTED,
            toState: WorkflowState.FINANCE_PROCESSING,
            isValid: true,
            badge: BadgeType.RE_SUBMITTED
        },
        {
            fromState: WorkflowState.FINANCE_PROCESSING,
            event: WorkflowEvent.RECEIVE_FROM_FINANCE,
            toState: WorkflowState.AUDIT_NEW_RESUBMITTED,
            isValid: true,
            badge: BadgeType.RE_SUBMITTED
        },
        {
            fromState: WorkflowState.AUDIT_NEW_RESUBMITTED,
            event: WorkflowEvent.START_WORK,
            toState: WorkflowState.AUDIT_PENDING_DISPATCH,
            isValid: true
        },
        // Return Flow
        {
            fromState: WorkflowState.AUDIT_NEW,
            event: WorkflowEvent.RETURN_VOUCHER,
            toState: WorkflowState.AUDIT_PENDING_DISPATCH_RETURNED,
            isValid: true,
            requiresCopy: true,
            copyState: WorkflowState.AUDIT_RETURNED_COPY,
            badge: BadgeType.RETURNED
        },
        {
            fromState: WorkflowState.AUDIT_PENDING_DISPATCH_RETURNED,
            event: WorkflowEvent.DISPATCH_TO_FINANCE,
            toState: WorkflowState.FINANCE_RETURNED,
            isValid: true
        },
        {
            fromState: WorkflowState.FINANCE_RETURNED,
            event: WorkflowEvent.RESUBMIT_FROM_RETURNED,
            toState: WorkflowState.FINANCE_PROCESSING,
            isValid: true,
            badge: BadgeType.RETURNED
        }
    ];
    static STATE_TO_TAB_MAPPING = {
        // Finance Dashboard Tabs
        [WorkflowState.FINANCE_PENDING]: 'pending',
        [WorkflowState.FINANCE_PROCESSING]: 'processing',
        [WorkflowState.FINANCE_CERTIFIED]: 'certified',
        [WorkflowState.FINANCE_REJECTED]: 'rejected',
        [WorkflowState.FINANCE_RETURNED]: 'returned',
        [WorkflowState.FINANCE_RESUBMISSION_RECEIVED]: 'certified', // Certified resubmissions go to CERTIFIED tab
        // Audit Dashboard Tabs
        [WorkflowState.AUDIT_NEW]: 'new-vouchers',
        [WorkflowState.AUDIT_NEW_RESUBMITTED]: 'new-vouchers',
        [WorkflowState.AUDIT_PENDING_DISPATCH]: 'pending-dispatch',
        [WorkflowState.AUDIT_PENDING_DISPATCH_REJECTED]: 'pending-dispatch',
        [WorkflowState.AUDIT_PENDING_DISPATCH_RETURNED]: 'pending-dispatch',
        [WorkflowState.AUDIT_DISPATCHED]: 'dispatched',
        [WorkflowState.AUDIT_REJECTED_COPY]: 'rejected',
        [WorkflowState.AUDIT_RETURNED_COPY]: 'returned'
    };
    /**
     * Get valid transition for given state and event
     */
    static getTransition(fromState, event) {
        return this.TRANSITIONS.find(t => t.fromState === fromState && t.event === event && t.isValid) || null;
    }
    /**
     * Get tab name for voucher based on workflow state and user department
     */
    static getTabForVoucher(workflowState, userDepartment) {
        // Only show vouchers relevant to user's department
        if (userDepartment === 'AUDIT') {
            if (workflowState.startsWith('AUDIT_')) {
                return this.STATE_TO_TAB_MAPPING[workflowState] || null;
            }
            // DUAL TAB VISIBILITY: Certified resubmissions should also appear in Audit DISPATCHED tab
            if (workflowState === WorkflowState.FINANCE_RESUBMISSION_RECEIVED) {
                return 'dispatched'; // Show in Audit DISPATCHED tab for dual visibility
            }
        }
        else {
            // Finance or original department
            if (workflowState.startsWith('FINANCE_')) {
                return this.STATE_TO_TAB_MAPPING[workflowState] || null;
            }
        }
        return null;
    }
    /**
     * Validate if transition is allowed
     */
    static canTransition(fromState, event) {
        const transition = this.getTransition(fromState, event);
        return transition !== null && transition.isValid;
    }
    /**
     * Get all possible events for a given state
     */
    static getValidEvents(fromState) {
        return this.TRANSITIONS
            .filter(t => t.fromState === fromState && t.isValid)
            .map(t => t.event);
    }
    /**
     * Get badge type for workflow state
     */
    static getBadgeType(workflowState, context) {
        if (workflowState === WorkflowState.AUDIT_NEW_RESUBMITTED) {
            return BadgeType.RE_SUBMITTED;
        }
        if (workflowState === WorkflowState.AUDIT_REJECTED_COPY) {
            return BadgeType.REJECTED;
        }
        if (workflowState === WorkflowState.AUDIT_RETURNED_COPY) {
            return BadgeType.RETURNED;
        }
        return BadgeType.NONE;
    }
    /**
     * Get all available tabs for department
     */
    static getAvailableTabs(userDepartment) {
        if (userDepartment === 'AUDIT') {
            return ['new-vouchers', 'pending-dispatch', 'dispatched', 'rejected', 'returned'];
        }
        else {
            return ['pending', 'processing', 'certified', 'rejected', 'returned'];
        }
    }
    /**
     * Check if voucher is editable based on workflow state
     */
    static isVoucherEditable(voucher, userDepartment) {
        const workflowState = voucher.workflow_state;
        if (userDepartment === 'AUDIT') {
            return workflowState === WorkflowState.AUDIT_NEW ||
                workflowState === WorkflowState.AUDIT_NEW_RESUBMITTED ||
                workflowState === WorkflowState.AUDIT_PENDING_DISPATCH;
        }
        // Finance users can edit pending vouchers
        return workflowState === WorkflowState.FINANCE_PENDING;
    }
}
exports.VoucherWorkflowStateMachine = VoucherWorkflowStateMachine;
//# sourceMappingURL=VoucherWorkflowStateMachine.js.map