const mysql = require('mysql2/promise');

async function cleanSystem() {
  const connection = await mysql.createConnection({
    host: 'localhost',
    user: 'root',
    password: 'vms@2025@1989',
    database: 'vms_production'
  });

  console.log('🧹 CLEANING VMS SYSTEM - REMOVING ALL VOUCHERS AND BATCHES');
  console.log('===========================================================');
  
  try {
    // Start transaction for safety
    await connection.beginTransaction();
    
    // 1. Clean batch-related tables first (foreign key dependencies)
    console.log('\n🗑️  Step 1: Cleaning batch_vouchers table...');
    const [batchVouchersResult] = await connection.execute('DELETE FROM batch_vouchers');
    console.log(`   ✅ Deleted ${batchVouchersResult.affectedRows} batch_voucher records`);
    
    console.log('\n🗑️  Step 2: Cleaning voucher_batches table...');
    const [voucherBatchesResult] = await connection.execute('DELETE FROM voucher_batches');
    console.log(`   ✅ Deleted ${voucherBatchesResult.affectedRows} voucher_batch records`);
    
    // 2. Clean voucher-related tables
    console.log('\n🗑️  Step 3: Cleaning vouchers table...');
    const [vouchersResult] = await connection.execute('DELETE FROM vouchers');
    console.log(`   ✅ Deleted ${vouchersResult.affectedRows} voucher records`);
    
    // 3. Clean notifications related to vouchers/batches
    console.log('\n🗑️  Step 4: Cleaning voucher/batch notifications...');
    const [notificationsResult] = await connection.execute(`
      DELETE FROM notifications 
      WHERE type IN ('NEW_VOUCHER', 'BATCH_RECEIVED', 'VOUCHER_RETURNED', 'VOUCHER_REJECTED', 'VOUCHER_CERTIFIED')
         OR message LIKE '%voucher%' 
         OR message LIKE '%batch%'
    `);
    console.log(`   ✅ Deleted ${notificationsResult.affectedRows} notification records`);
    
    // 4. Clean audit logs related to vouchers/batches
    console.log('\n🗑️  Step 5: Cleaning audit logs...');
    const [auditResult] = await connection.execute(`
      DELETE FROM audit_logs 
      WHERE action LIKE '%voucher%' 
         OR action LIKE '%batch%'
         OR details LIKE '%voucher%'
         OR details LIKE '%batch%'
    `);
    console.log(`   ✅ Deleted ${auditResult.affectedRows} audit log records`);
    
    // 5. Reset auto-increment counters
    console.log('\n🔄 Step 6: Resetting auto-increment counters...');
    await connection.execute('ALTER TABLE vouchers AUTO_INCREMENT = 1');
    await connection.execute('ALTER TABLE voucher_batches AUTO_INCREMENT = 1');
    await connection.execute('ALTER TABLE batch_vouchers AUTO_INCREMENT = 1');
    console.log('   ✅ Auto-increment counters reset');
    
    // Commit transaction
    await connection.commit();
    
    // 6. Verify cleanup
    console.log('\n🔍 Step 7: Verifying cleanup...');
    const [voucherCount] = await connection.execute('SELECT COUNT(*) as count FROM vouchers');
    const [batchCount] = await connection.execute('SELECT COUNT(*) as count FROM voucher_batches');
    const [batchVoucherCount] = await connection.execute('SELECT COUNT(*) as count FROM batch_vouchers');
    
    console.log(`   📊 Remaining vouchers: ${voucherCount[0].count}`);
    console.log(`   📊 Remaining batches: ${batchCount[0].count}`);
    console.log(`   📊 Remaining batch_vouchers: ${batchVoucherCount[0].count}`);
    
    if (voucherCount[0].count === 0 && batchCount[0].count === 0 && batchVoucherCount[0].count === 0) {
      console.log('\n🎉 SUCCESS: System completely cleaned!');
      console.log('   ✅ All vouchers removed');
      console.log('   ✅ All batches removed');
      console.log('   ✅ All batch-voucher links removed');
      console.log('   ✅ Related notifications cleaned');
      console.log('   ✅ Related audit logs cleaned');
      console.log('\n🚀 System is ready for fresh data!');
    } else {
      console.log('\n⚠️  WARNING: Some records may still exist');
    }
    
  } catch (error) {
    // Rollback on error
    await connection.rollback();
    console.error('\n❌ ERROR during cleanup:', error.message);
    console.log('   🔄 Transaction rolled back - no changes made');
    throw error;
  } finally {
    await connection.end();
  }
}

// Run the cleanup
cleanSystem().catch(console.error);
