const mysql = require('mysql2/promise');

async function cleanSystemComplete() {
  console.log('🧹 CLEANING SYSTEM - REMOVING ALL VOUCHERS AND BATCHES');
  console.log('====================================================');
  
  const connection = await mysql.createConnection({
    host: 'localhost',
    user: 'root',
    password: 'vms@2025@1989',
    database: 'vms_production'
  });
  
  try {
    console.log('🔍 Checking current system state...');
    
    // Check current vouchers
    const [voucherCount] = await connection.execute('SELECT COUNT(*) as count FROM vouchers');
    console.log(`📊 Current vouchers: ${voucherCount[0].count}`);
    
    // Check current batches
    const [batchCount] = await connection.execute('SELECT COUNT(*) as count FROM voucher_batches');
    console.log(`📦 Current batches: ${batchCount[0].count}`);
    
    // Check batch_vouchers
    const [batchVoucherCount] = await connection.execute('SELECT COUNT(*) as count FROM batch_vouchers');
    console.log(`🔗 Current batch-voucher links: ${batchVoucherCount[0].count}`);
    
    // Check notifications
    const [notificationCount] = await connection.execute('SELECT COUNT(*) as count FROM notifications');
    console.log(`🔔 Current notifications: ${notificationCount[0].count}`);
    
    if (voucherCount[0].count === 0 && batchCount[0].count === 0) {
      console.log('✅ System is already clean - no vouchers or batches found');
      return;
    }
    
    console.log('\n🗑️ Starting cleanup process...');
    
    // Step 1: Delete batch_vouchers (junction table)
    console.log('1️⃣ Cleaning batch-voucher relationships...');
    const [batchVoucherResult] = await connection.execute('DELETE FROM batch_vouchers');
    console.log(`   ✅ Deleted ${batchVoucherResult.affectedRows} batch-voucher links`);
    
    // Step 2: Delete voucher_batches
    console.log('2️⃣ Cleaning voucher batches...');
    const [batchResult] = await connection.execute('DELETE FROM voucher_batches');
    console.log(`   ✅ Deleted ${batchResult.affectedRows} batches`);
    
    // Step 3: Delete vouchers
    console.log('3️⃣ Cleaning vouchers...');
    const [voucherResult] = await connection.execute('DELETE FROM vouchers');
    console.log(`   ✅ Deleted ${voucherResult.affectedRows} vouchers`);
    
    // Step 4: Clean notifications
    console.log('4️⃣ Cleaning notifications...');
    const [notificationResult] = await connection.execute('DELETE FROM notifications');
    console.log(`   ✅ Deleted ${notificationResult.affectedRows} notifications`);
    
    // Step 5: Clean audit_logs if exists
    console.log('5️⃣ Cleaning audit logs...');
    try {
      const [auditResult] = await connection.execute('DELETE FROM audit_logs');
      console.log(`   ✅ Deleted ${auditResult.affectedRows} audit log entries`);
    } catch (error) {
      if (error.code === 'ER_NO_SUCH_TABLE') {
        console.log('   ℹ️ No audit_logs table found (skipping)');
      } else {
        console.log(`   ⚠️ Error cleaning audit logs: ${error.message}`);
      }
    }
    
    // Step 6: Reset AUTO_INCREMENT counters
    console.log('6️⃣ Resetting auto-increment counters...');
    try {
      await connection.execute('ALTER TABLE vouchers AUTO_INCREMENT = 1');
      await connection.execute('ALTER TABLE voucher_batches AUTO_INCREMENT = 1');
      await connection.execute('ALTER TABLE notifications AUTO_INCREMENT = 1');
      console.log('   ✅ Auto-increment counters reset');
    } catch (error) {
      console.log(`   ⚠️ Note: ${error.message} (this is usually fine)`);
    }
    
    // Step 7: Verify cleanup
    console.log('\n🔍 Verifying cleanup...');
    
    const [finalVoucherCount] = await connection.execute('SELECT COUNT(*) as count FROM vouchers');
    const [finalBatchCount] = await connection.execute('SELECT COUNT(*) as count FROM voucher_batches');
    const [finalBatchVoucherCount] = await connection.execute('SELECT COUNT(*) as count FROM batch_vouchers');
    const [finalNotificationCount] = await connection.execute('SELECT COUNT(*) as count FROM notifications');
    
    console.log(`📊 Final vouchers: ${finalVoucherCount[0].count}`);
    console.log(`📦 Final batches: ${finalBatchCount[0].count}`);
    console.log(`🔗 Final batch-voucher links: ${finalBatchVoucherCount[0].count}`);
    console.log(`🔔 Final notifications: ${finalNotificationCount[0].count}`);
    
    const isClean = finalVoucherCount[0].count === 0 && 
                   finalBatchCount[0].count === 0 && 
                   finalBatchVoucherCount[0].count === 0 && 
                   finalNotificationCount[0].count === 0;
    
    if (isClean) {
      console.log('\n🎉 SYSTEM CLEANUP COMPLETED SUCCESSFULLY!');
      console.log('✅ All vouchers removed');
      console.log('✅ All batches removed');
      console.log('✅ All batch-voucher links removed');
      console.log('✅ All notifications removed');
      console.log('✅ System is now clean and ready for fresh data');
      console.log('');
      console.log('🚀 You can now:');
      console.log('   • Create new vouchers');
      console.log('   • Test workflows from scratch');
      console.log('   • Verify all functionality works correctly');
      console.log('');
      console.log('💡 Remember to refresh your browser (Ctrl+F5) to clear any cached data');
    } else {
      console.log('\n❌ CLEANUP INCOMPLETE - Some data may remain');
      console.log('Please check the database manually or run the script again');
    }
    
  } catch (error) {
    console.error('❌ Error during system cleanup:', error);
    console.log('\n🔧 Troubleshooting:');
    console.log('1. Check database connection');
    console.log('2. Verify database permissions');
    console.log('3. Ensure no foreign key constraints are blocking deletion');
  } finally {
    await connection.end();
  }
}

cleanSystemComplete().catch(console.error);
