const mysql = require('mysql2/promise');

async function debugActualIssue() {
  console.log('🔍 DEBUGGING ACTUAL RESUBMISSION ISSUE');
  console.log('=====================================');
  console.log('Checking the real voucher FINJUL0002 that was tested');
  
  const connection = await mysql.createConnection({
    host: 'localhost',
    user: 'root',
    password: 'vms@2025@1989',
    database: 'vms_production'
  });
  
  try {
    // Find the actual voucher that was tested
    const [vouchers] = await connection.execute(`
      SELECT id, voucher_id, status, workflow_state, department, original_department,
             is_resubmitted, rejected_by, rejection_time, comment, certified_by,
             received_by, department_received_by, finance_received, dispatched_by,
             audit_dispatched_by, batch_id, created_at
      FROM vouchers 
      WHERE voucher_id LIKE '%FINJUL0002%' OR voucher_id = 'FINJUL0002'
      ORDER BY created_at DESC
      LIMIT 5
    `);
    
    if (vouchers.length === 0) {
      console.log('❌ No voucher found with ID FINJUL0002');
      console.log('🔍 Let me check all recent vouchers...');
      
      const [recentVouchers] = await connection.execute(`
        SELECT id, voucher_id, status, workflow_state, department, original_department,
               is_resubmitted, rejected_by, certified_by, finance_received
        FROM vouchers 
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
        ORDER BY created_at DESC
        LIMIT 10
      `);
      
      console.log(`📊 Found ${recentVouchers.length} recent vouchers:`);
      recentVouchers.forEach((v, i) => {
        console.log(`   ${i+1}. ${v.voucher_id} - ${v.status} - ${v.workflow_state} - ${v.department}`);
      });
      
      return;
    }
    
    console.log(`📊 Found ${vouchers.length} voucher(s) with FINJUL0002:`);
    
    vouchers.forEach((voucher, index) => {
      console.log(`\n📋 VOUCHER ${index + 1}:`);
      console.log('==================');
      console.log('   id:', voucher.id);
      console.log('   voucher_id:', voucher.voucher_id);
      console.log('   status:', voucher.status);
      console.log('   workflow_state:', voucher.workflow_state);
      console.log('   department:', voucher.department);
      console.log('   original_department:', voucher.original_department);
      console.log('   is_resubmitted:', voucher.is_resubmitted);
      console.log('   rejected_by:', voucher.rejected_by);
      console.log('   rejection_time:', voucher.rejection_time);
      console.log('   comment:', voucher.comment);
      console.log('   certified_by:', voucher.certified_by);
      console.log('   received_by:', voucher.received_by);
      console.log('   department_received_by:', voucher.department_received_by);
      console.log('   finance_received:', voucher.finance_received);
      console.log('   dispatched_by:', voucher.dispatched_by);
      console.log('   audit_dispatched_by:', voucher.audit_dispatched_by);
      console.log('   batch_id:', voucher.batch_id);
      console.log('   created_at:', voucher.created_at);
      
      // Analyze what tab this should appear in
      console.log('\n🔍 TAB ANALYSIS:');
      console.log('================');
      
      // Finance CERTIFIED tab logic
      const financeTabLogic = voucher.workflow_state === 'AUDIT_DISPATCHED' && 
                             voucher.status === 'VOUCHER CERTIFIED' && 
                             voucher.finance_received === 1 &&
                             voucher.department === 'FINANCE';
      
      console.log('   Finance CERTIFIED Tab Logic:');
      console.log(`     workflow_state === 'AUDIT_DISPATCHED': ${voucher.workflow_state === 'AUDIT_DISPATCHED'}`);
      console.log(`     status === 'VOUCHER CERTIFIED': ${voucher.status === 'VOUCHER CERTIFIED'}`);
      console.log(`     finance_received === 1: ${voucher.finance_received === 1}`);
      console.log(`     department === 'FINANCE': ${voucher.department === 'FINANCE'}`);
      console.log(`     ✅ Should appear in Finance CERTIFIED: ${financeTabLogic}`);
      
      // Audit DISPATCHED tab logic
      const auditTabLogic = voucher.workflow_state === 'AUDIT_DISPATCHED' && 
                           voucher.audit_dispatched_by;
      
      console.log('\n   Audit DISPATCHED Tab Logic:');
      console.log(`     workflow_state === 'AUDIT_DISPATCHED': ${voucher.workflow_state === 'AUDIT_DISPATCHED'}`);
      console.log(`     audit_dispatched_by exists: ${!!voucher.audit_dispatched_by}`);
      console.log(`     ✅ Should appear in Audit DISPATCHED: ${auditTabLogic}`);
      
      // Badge logic
      const badgeLogic = voucher.is_resubmitted === 1;
      const isCertified = voucher.certified_by || voucher.department_received_by;
      const expectedBadge = badgeLogic ? (isCertified ? 'CERTIFIED-RESUBMISSION' : 'RESUBMISSION') : 'NONE';
      
      console.log('\n   Badge Logic:');
      console.log(`     is_resubmitted === 1: ${voucher.is_resubmitted === 1}`);
      console.log(`     certified_by or department_received_by exists: ${!!isCertified}`);
      console.log(`     ✅ Expected Badge: ${expectedBadge}`);
      
      // Overall assessment
      console.log('\n🎯 ISSUE ASSESSMENT:');
      console.log('====================');
      
      if (!financeTabLogic) {
        console.log('❌ PROBLEM: Voucher will NOT appear in Finance CERTIFIED tab');
        console.log('   Reasons:');
        if (voucher.workflow_state !== 'AUDIT_DISPATCHED') {
          console.log(`     - workflow_state is '${voucher.workflow_state}' instead of 'AUDIT_DISPATCHED'`);
        }
        if (voucher.status !== 'VOUCHER CERTIFIED') {
          console.log(`     - status is '${voucher.status}' instead of 'VOUCHER CERTIFIED'`);
        }
        if (voucher.finance_received !== 1) {
          console.log(`     - finance_received is '${voucher.finance_received}' instead of 1`);
        }
        if (voucher.department !== 'FINANCE') {
          console.log(`     - department is '${voucher.department}' instead of 'FINANCE'`);
        }
      } else {
        console.log('✅ GOOD: Voucher should appear in Finance CERTIFIED tab');
      }
      
      if (!auditTabLogic) {
        console.log('❌ PROBLEM: Voucher will NOT appear in Audit DISPATCHED tab');
        console.log('   Reasons:');
        if (voucher.workflow_state !== 'AUDIT_DISPATCHED') {
          console.log(`     - workflow_state is '${voucher.workflow_state}' instead of 'AUDIT_DISPATCHED'`);
        }
        if (!voucher.audit_dispatched_by) {
          console.log(`     - audit_dispatched_by is missing`);
        }
      } else {
        console.log('✅ GOOD: Voucher should appear in Audit DISPATCHED tab');
      }
    });
    
    // Check if there are any batches related to this voucher
    console.log('\n📦 BATCH ANALYSIS:');
    console.log('==================');
    
    const voucherIds = vouchers.map(v => v.id);
    if (voucherIds.length > 0) {
      const [batches] = await connection.execute(`
        SELECT vb.id as batch_id, vb.department, vb.sent_by, vb.sent_time, 
               vb.received, vb.from_audit, bv.voucher_id
        FROM voucher_batches vb
        JOIN batch_vouchers bv ON vb.id = bv.batch_id
        WHERE bv.voucher_id IN (${voucherIds.map(() => '?').join(',')})
        ORDER BY vb.sent_time DESC
      `, voucherIds);
      
      console.log(`📊 Found ${batches.length} batch(es) for this voucher:`);
      batches.forEach((batch, i) => {
        console.log(`   ${i+1}. Batch ${batch.batch_id}:`);
        console.log(`      department: ${batch.department}`);
        console.log(`      sent_by: ${batch.sent_by}`);
        console.log(`      received: ${batch.received}`);
        console.log(`      from_audit: ${batch.from_audit}`);
        console.log(`      sent_time: ${batch.sent_time}`);
      });
    }
    
  } catch (error) {
    console.error('❌ Error debugging actual issue:', error);
  } finally {
    await connection.end();
  }
}

debugActualIssue().catch(console.error);
