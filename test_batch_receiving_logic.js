const mysql = require('mysql2/promise');
const { v4: uuidv4 } = require('uuid');

async function testBatchReceivingLogic() {
  console.log('🧪 TESTING BATCH RECEIVING LOGIC FOR BOTH WORKFLOWS');
  console.log('==================================================');
  
  const connection = await mysql.createConnection({
    host: 'localhost',
    user: 'root',
    password: 'vms@2025@1989',
    database: 'vms_production'
  });
  
  try {
    // Test 1: Normal Voucher Batch Receiving
    console.log('\n📋 TEST 1: NORMAL VOUCHER BATCH RECEIVING');
    console.log('==========================================');
    
    const normalVoucherId = uuidv4();
    const normalVoucherCode = 'NORMAL' + Date.now();
    
    // Create normal voucher
    await connection.execute(`
      INSERT INTO vouchers (
        id, voucher_id, date, claimant, description, amount, currency,
        department, original_department, status, workflow_state,
        created_by, created_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
    `, [
      normalVoucherId, normalVoucherCode, '2025-01-21', 'Normal Claimant', 'Normal voucher test',
      1000.00, 'GHS', 'FINANCE', 'FINANCE', 'PENDING RECEIPT', 'FINANCE_PROCESSING',
      'TEST USER'
    ]);
    
    console.log(`✅ Created normal voucher: ${normalVoucherCode}`);
    
    // Create batch for normal voucher
    const normalBatchId = uuidv4();
    await connection.execute(`
      INSERT INTO voucher_batches (
        id, department, sent_by, sent_time, received, from_audit
      ) VALUES (?, ?, ?, NOW(), ?, ?)
    `, [normalBatchId, 'AUDIT', 'TEST USER', false, false]);
    
    // Add voucher to batch
    await connection.execute(`
      INSERT INTO batch_vouchers (batch_id, voucher_id) VALUES (?, ?)
    `, [normalBatchId, normalVoucherId]);
    
    // Update voucher with batch
    await connection.execute(`
      UPDATE vouchers SET batch_id = ? WHERE id = ?
    `, [normalBatchId, normalVoucherId]);
    
    console.log('✅ Normal voucher added to batch');
    
    // Simulate Audit receiving normal voucher
    console.log('\n🔄 Audit receives normal voucher...');
    await connection.execute(`
      UPDATE vouchers SET 
        status = 'VOUCHER PROCESSING',
        workflow_state = 'AUDIT_NEW',
        department = 'AUDIT',
        received_by = 'AUDIT USER',
        receipt_time = NOW(),
        is_resubmitted = 0
      WHERE id = ?
    `, [normalVoucherId]);
    
    // Verify normal voucher state
    const [normalResult] = await connection.execute(`
      SELECT voucher_id, status, workflow_state, department, is_resubmitted
      FROM vouchers WHERE id = ?
    `, [normalVoucherId]);
    
    const normalVoucher = normalResult[0];
    console.log('📊 Normal Voucher Final State:');
    console.log('   voucher_id:', normalVoucher.voucher_id);
    console.log('   status:', normalVoucher.status);
    console.log('   workflow_state:', normalVoucher.workflow_state);
    console.log('   department:', normalVoucher.department);
    console.log('   is_resubmitted:', normalVoucher.is_resubmitted);
    
    const normalSuccess = normalVoucher.status === 'VOUCHER PROCESSING' &&
                         normalVoucher.workflow_state === 'AUDIT_NEW' &&
                         normalVoucher.department === 'AUDIT' &&
                         normalVoucher.is_resubmitted === 0;
    
    console.log('✅ Normal Voucher Batch Receiving:', normalSuccess ? 'SUCCESS' : 'FAILED');
    
    // Test 2: Resubmission Voucher Batch Receiving
    console.log('\n📋 TEST 2: RESUBMISSION VOUCHER BATCH RECEIVING');
    console.log('===============================================');
    
    const resubVoucherId = uuidv4();
    const resubVoucherCode = 'RESUB' + Date.now();
    
    // Create resubmission voucher (previously rejected)
    await connection.execute(`
      INSERT INTO vouchers (
        id, voucher_id, date, claimant, description, amount, currency,
        department, original_department, status, workflow_state,
        created_by, created_at, is_resubmitted, rejected_by, rejection_time, comment
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), ?, ?, NOW(), ?)
    `, [
      resubVoucherId, resubVoucherCode, '2025-01-21', 'Resubmission Claimant', 'Resubmission voucher test',
      1500.00, 'GHS', 'FINANCE', 'FINANCE', 'PENDING RECEIPT', 'FINANCE_PROCESSING',
      'TEST USER', 1, 'AUDIT USER', 'Missing documents - resubmitted'
    ]);
    
    console.log(`✅ Created resubmission voucher: ${resubVoucherCode}`);
    
    // Create batch for resubmission voucher
    const resubBatchId = uuidv4();
    await connection.execute(`
      INSERT INTO voucher_batches (
        id, department, sent_by, sent_time, received, from_audit
      ) VALUES (?, ?, ?, NOW(), ?, ?)
    `, [resubBatchId, 'AUDIT', 'TEST USER', false, false]);
    
    // Add voucher to batch
    await connection.execute(`
      INSERT INTO batch_vouchers (batch_id, voucher_id) VALUES (?, ?)
    `, [resubBatchId, resubVoucherId]);
    
    // Update voucher with batch
    await connection.execute(`
      UPDATE vouchers SET batch_id = ? WHERE id = ?
    `, [resubBatchId, resubVoucherId]);
    
    console.log('✅ Resubmission voucher added to batch');
    
    // Simulate Audit receiving resubmission voucher
    console.log('\n🔄 Audit receives resubmission voucher...');
    await connection.execute(`
      UPDATE vouchers SET 
        status = 'VOUCHER PROCESSING',
        workflow_state = 'AUDIT_NEW_RESUBMITTED',
        department = 'AUDIT',
        received_by = 'AUDIT USER',
        receipt_time = NOW(),
        is_resubmitted = 1
      WHERE id = ?
    `, [resubVoucherId]);
    
    // Verify resubmission voucher state
    const [resubResult] = await connection.execute(`
      SELECT voucher_id, status, workflow_state, department, is_resubmitted, rejected_by, comment
      FROM vouchers WHERE id = ?
    `, [resubVoucherId]);
    
    const resubVoucher = resubResult[0];
    console.log('📊 Resubmission Voucher Final State:');
    console.log('   voucher_id:', resubVoucher.voucher_id);
    console.log('   status:', resubVoucher.status);
    console.log('   workflow_state:', resubVoucher.workflow_state);
    console.log('   department:', resubVoucher.department);
    console.log('   is_resubmitted:', resubVoucher.is_resubmitted);
    console.log('   rejected_by:', resubVoucher.rejected_by);
    console.log('   comment:', resubVoucher.comment);
    
    const resubSuccess = resubVoucher.status === 'VOUCHER PROCESSING' &&
                        resubVoucher.workflow_state === 'AUDIT_NEW_RESUBMITTED' &&
                        resubVoucher.department === 'AUDIT' &&
                        resubVoucher.is_resubmitted === 1 &&
                        resubVoucher.rejected_by === 'AUDIT USER';
    
    console.log('✅ Resubmission Voucher Batch Receiving:', resubSuccess ? 'SUCCESS' : 'FAILED');
    
    // Test 3: Rejected Voucher Batch Receiving
    console.log('\n📋 TEST 3: REJECTED VOUCHER BATCH RECEIVING');
    console.log('===========================================');
    
    const rejectedVoucherId = uuidv4();
    const rejectedVoucherCode = 'REJECTED' + Date.now();
    
    // Create rejected voucher
    await connection.execute(`
      INSERT INTO vouchers (
        id, voucher_id, date, claimant, description, amount, currency,
        department, original_department, status, workflow_state,
        created_by, created_at, rejected_by, rejection_time, comment
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), ?, NOW(), ?)
    `, [
      rejectedVoucherId, rejectedVoucherCode, '2025-01-21', 'Rejected Claimant', 'Rejected voucher test',
      2000.00, 'GHS', 'AUDIT', 'FINANCE', 'VOUCHER REJECTED', 'AUDIT_PENDING_DISPATCH_REJECTED',
      'TEST USER', 'AUDIT USER', 'Insufficient documentation'
    ]);
    
    console.log(`✅ Created rejected voucher: ${rejectedVoucherCode}`);
    
    // Create batch for rejected voucher (Audit sending back to Finance)
    const rejectedBatchId = uuidv4();
    await connection.execute(`
      INSERT INTO voucher_batches (
        id, department, sent_by, sent_time, received, from_audit
      ) VALUES (?, ?, ?, NOW(), ?, ?)
    `, [rejectedBatchId, 'FINANCE', 'AUDIT USER', false, true]);
    
    // Add voucher to batch
    await connection.execute(`
      INSERT INTO batch_vouchers (batch_id, voucher_id) VALUES (?, ?)
    `, [rejectedBatchId, rejectedVoucherId]);
    
    // Update voucher with batch
    await connection.execute(`
      UPDATE vouchers SET batch_id = ? WHERE id = ?
    `, [rejectedBatchId, rejectedVoucherId]);
    
    console.log('✅ Rejected voucher added to batch');
    
    // Simulate Finance receiving rejected voucher
    console.log('\n🔄 Finance receives rejected voucher...');
    await connection.execute(`
      UPDATE vouchers SET 
        status = 'VOUCHER REJECTED',
        workflow_state = 'FINANCE_REJECTED',
        department = 'FINANCE',
        received_by = 'FINANCE USER',
        receipt_time = NOW()
      WHERE id = ?
    `, [rejectedVoucherId]);
    
    // Verify rejected voucher state
    const [rejectedResult] = await connection.execute(`
      SELECT voucher_id, status, workflow_state, department, rejected_by, comment
      FROM vouchers WHERE id = ?
    `, [rejectedVoucherId]);
    
    const rejectedVoucher = rejectedResult[0];
    console.log('📊 Rejected Voucher Final State:');
    console.log('   voucher_id:', rejectedVoucher.voucher_id);
    console.log('   status:', rejectedVoucher.status);
    console.log('   workflow_state:', rejectedVoucher.workflow_state);
    console.log('   department:', rejectedVoucher.department);
    console.log('   rejected_by:', rejectedVoucher.rejected_by);
    console.log('   comment:', rejectedVoucher.comment);
    
    const rejectedSuccess = rejectedVoucher.status === 'VOUCHER REJECTED' &&
                           rejectedVoucher.workflow_state === 'FINANCE_REJECTED' &&
                           rejectedVoucher.department === 'FINANCE' &&
                           rejectedVoucher.rejected_by === 'AUDIT USER';
    
    console.log('✅ Rejected Voucher Batch Receiving:', rejectedSuccess ? 'SUCCESS' : 'FAILED');
    
    // Final Summary
    console.log('\n🎯 BATCH RECEIVING LOGIC TEST SUMMARY:');
    console.log('======================================');
    console.log('✅ Normal Voucher:', normalSuccess ? 'PASS' : 'FAIL');
    console.log('✅ Resubmission Voucher:', resubSuccess ? 'PASS' : 'FAIL');
    console.log('✅ Rejected Voucher:', rejectedSuccess ? 'PASS' : 'FAIL');
    
    if (normalSuccess && resubSuccess && rejectedSuccess) {
      console.log('\n🎉 ALL BATCH RECEIVING TESTS PASSED!');
      console.log('✅ Normal vouchers flow correctly');
      console.log('✅ Resubmissions preserve flags and history');
      console.log('✅ Rejected vouchers maintain rejection state');
      console.log('✅ No interference between different workflow types');
    } else {
      console.log('\n❌ SOME BATCH RECEIVING TESTS FAILED!');
    }
    
    // Clean up test vouchers
    await connection.execute('DELETE FROM batch_vouchers WHERE batch_id IN (?, ?, ?)', [normalBatchId, resubBatchId, rejectedBatchId]);
    await connection.execute('DELETE FROM voucher_batches WHERE id IN (?, ?, ?)', [normalBatchId, resubBatchId, rejectedBatchId]);
    await connection.execute('DELETE FROM vouchers WHERE id IN (?, ?, ?)', [normalVoucherId, resubVoucherId, rejectedVoucherId]);
    console.log('\n🧹 Test vouchers and batches cleaned up');
    
  } catch (error) {
    console.error('❌ Error testing batch receiving logic:', error);
  } finally {
    await connection.end();
  }
}

testBatchReceivingLogic().catch(console.error);
