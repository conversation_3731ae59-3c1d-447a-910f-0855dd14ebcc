const mysql = require('mysql2/promise');
require('dotenv').config();

async function checkBatchStatus() {
  let connection;
  
  try {
    // Create connection
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || 'vms@2025@1989',
      database: process.env.DB_NAME || 'vms_production'
    });

    console.log('🔗 Connected to database');
    console.log('🔍 Checking current batch and voucher status...\n');

    // Check recent batches
    const [batches] = await connection.execute(`
      SELECT
        id,
        department,
        sent_by,
        sent_time,
        received,
        contains_resubmissions,
        resubmission_count
      FROM voucher_batches
      ORDER BY sent_time DESC
      LIMIT 3
    `);

    console.log('📦 Recent Batches:');
    batches.forEach((batch, index) => {
      console.log(`--- Batch ${index + 1} ---`);
      console.log(`Batch ID: ${batch.id}`);
      console.log(`Department: ${batch.department}`);
      console.log(`Sent By: ${batch.sent_by}`);
      console.log(`Sent Time: ${batch.sent_time}`);
      console.log(`Received: ${batch.received ? 'YES' : 'NO'}`);
      console.log(`Contains Resubmissions: ${batch.contains_resubmissions ? 'YES' : 'NO'}`);
      console.log(`Resubmission Count: ${batch.resubmission_count || 0}`);
      console.log('');
    });

    // Check KIM voucher current status
    const [kimVouchers] = await connection.execute(`
      SELECT 
        voucher_id, 
        claimant, 
        status, 
        workflow_state, 
        is_resubmission, 
        sent_to_audit,
        batch_id
      FROM vouchers 
      WHERE claimant = 'KIM' AND voucher_id = 'FINJUL0004'
    `);

    console.log('📊 KIM Voucher Status:');
    if (kimVouchers.length > 0) {
      const voucher = kimVouchers[0];
      console.log(`Voucher ID: ${voucher.voucher_id}`);
      console.log(`Status: ${voucher.status}`);
      console.log(`Workflow State: ${voucher.workflow_state}`);
      console.log(`Is Resubmission: ${voucher.is_resubmission ? 'YES' : 'NO'}`);
      console.log(`Sent to Audit: ${voucher.sent_to_audit ? 'YES' : 'NO'}`);
      console.log(`Batch ID: ${voucher.batch_id || 'None'}`);
    }

    // Check batch-voucher relationships
    const [batchVouchers] = await connection.execute(`
      SELECT 
        bv.batch_id,
        bv.voucher_id,
        bv.is_resubmission,
        v.claimant,
        v.status
      FROM batch_vouchers bv
      JOIN vouchers v ON bv.voucher_id = v.voucher_id
      WHERE v.claimant = 'KIM'
      ORDER BY bv.created_at DESC
      LIMIT 3
    `);

    console.log('\n🔗 Recent Batch-Voucher Links for KIM:');
    batchVouchers.forEach((bv, index) => {
      console.log(`--- Link ${index + 1} ---`);
      console.log(`Batch ID: ${bv.batch_id}`);
      console.log(`Voucher ID: ${bv.voucher_id}`);
      console.log(`Claimant: ${bv.claimant}`);
      console.log(`Status: ${bv.status}`);
      console.log(`Is Resubmission in Batch: ${bv.is_resubmission ? 'YES' : 'NO'}`);
      console.log('');
    });

  } catch (error) {
    console.error('❌ Error checking batch status:', error);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 Database connection closed');
    }
  }
}

// Run the check
checkBatchStatus();
