/**
 * Frontend Voucher Workflow State Machine
 * Single Source of Truth for voucher tab filtering and state management
 */

export enum WorkflowState {
  // Finance States
  FINANCE_PENDING = 'FINANCE_PENDING',
  FINANCE_PROCESSING = 'FINANCE_PROCESSING', 
  FINANCE_CERTIFIED = 'FINANCE_CERTIFIED',
  FINANCE_REJECTED = 'FINANCE_REJECTED',
  FINANCE_RETURNED = 'FINANCE_RETURNED',

  // Audit States
  AUDIT_NEW = 'AUDIT_NEW',
  AUDIT_NEW_RESUBMITTED = 'AUDIT_NEW_RESUBMITTED',
  AUDIT_PENDING_DISPATCH = 'AUDIT_PENDING_DISPATCH',
  AUDIT_PENDING_DISPATCH_REJECTED = 'AUDIT_PENDING_DISPATCH_REJECTED',
  AUDIT_PENDING_DISPATCH_RETURNED = 'AUDIT_PENDING_DISPATCH_RETURNED',
  AUDIT_DISPATCHED = 'AUDIT_DISPATCHED',
  AUDIT_REJECTED_COPY = 'AUDIT_REJECTED_COPY',
  AUDIT_RETURNED_COPY = 'AUDIT_RETURNED_COPY'
}

export enum BadgeType {
  NONE = 'NONE',
  RE_SUBMITTED = 'RE_SUBMITTED',
  RETURNED = 'RETURNED',
  REJECTED = 'REJECTED'
}

export interface Voucher {
  id: string;
  voucher_id: string;
  workflow_state: WorkflowState;
  badge_type: BadgeType;
  department: string;
  original_department: string;
  is_copy: boolean;
  parent_voucher_id?: string;
  version: number;
  [key: string]: any;
}

export interface TabCounts {
  [tabName: string]: number;
}

export class VoucherWorkflowStateMachine {
  private static readonly STATE_TO_TAB_MAPPING = {
    // Finance Dashboard Tabs
    [WorkflowState.FINANCE_PENDING]: 'pending',
    [WorkflowState.FINANCE_PROCESSING]: 'processing',
    [WorkflowState.FINANCE_CERTIFIED]: 'certified',
    [WorkflowState.FINANCE_REJECTED]: 'rejected',
    [WorkflowState.FINANCE_RETURNED]: 'returned',

    // Audit Dashboard Tabs
    [WorkflowState.AUDIT_NEW]: 'new-vouchers',
    [WorkflowState.AUDIT_NEW_RESUBMITTED]: 'new-vouchers',
    [WorkflowState.AUDIT_PENDING_DISPATCH]: 'pending-dispatch',
    [WorkflowState.AUDIT_PENDING_DISPATCH_REJECTED]: 'pending-dispatch',
    [WorkflowState.AUDIT_PENDING_DISPATCH_RETURNED]: 'pending-dispatch',
    [WorkflowState.AUDIT_DISPATCHED]: 'dispatched',
    [WorkflowState.AUDIT_REJECTED_COPY]: 'rejected',
    [WorkflowState.AUDIT_RETURNED_COPY]: 'returned'
  };

  private static readonly TAB_DISPLAY_NAMES = {
    // Finance Tabs
    'pending': 'Pending',
    'processing': 'Processing',
    'certified': 'Certified',
    'rejected': 'Rejected',
    'returned': 'Returned',

    // Audit Tabs
    'new-vouchers': 'New Vouchers',
    'pending-dispatch': 'Pending Dispatch',
    'dispatched': 'Dispatched'
  };

  private static readonly BADGE_DISPLAY_NAMES = {
    [BadgeType.NONE]: '',
    [BadgeType.RE_SUBMITTED]: 'RE-SUBMITTED',
    [BadgeType.RETURNED]: 'RETURNED',
    [BadgeType.REJECTED]: 'REJECTED'
  };

  private static readonly BADGE_COLORS = {
    [BadgeType.NONE]: '',
    [BadgeType.RE_SUBMITTED]: 'bg-blue-100 text-blue-800',
    [BadgeType.RETURNED]: 'bg-yellow-100 text-yellow-800',
    [BadgeType.REJECTED]: 'bg-red-100 text-red-800'
  };

  /**
   * Get tab name for voucher based on workflow state and user department
   */
  static getTabForVoucher(voucher: Voucher, userDepartment: string): string | null {
    const { workflow_state, department, original_department, status, workStarted, dispatched } = voucher;

    // FALLBACK: If workflow_state is not set, use legacy status-based logic
    if (!workflow_state) {
      return this.getLegacyTabForVoucher(voucher, userDepartment);
    }

    // New workflow state machine logic
    if (userDepartment === 'AUDIT') {
      if (workflow_state.startsWith('AUDIT_')) {
        return this.STATE_TO_TAB_MAPPING[workflow_state] || null;
      }
      return null;
    }

    // Finance users see finance-related vouchers for their department
    if (workflow_state.startsWith('FINANCE_') && original_department === userDepartment) {
      return this.STATE_TO_TAB_MAPPING[workflow_state] || null;
    }

    return null;
  }

  /**
   * Legacy tab filtering logic for vouchers without workflow_state
   */
  static getLegacyTabForVoucher(voucher: Voucher, userDepartment: string): string | null {
    const { status, department, originalDepartment, workStarted, dispatched, receivedByAudit } = voucher;

    if (userDepartment === 'AUDIT') {
      // NEW VOUCHERS: Vouchers received by audit but not worked on yet
      if (status === 'AUDIT: PROCESSING' && department === 'AUDIT' && receivedByAudit && !workStarted) {
        return 'new-vouchers';
      }

      // NEW VOUCHERS: Resubmitted vouchers received by audit
      if (status === 'RE-SUBMISSION' && department === 'AUDIT' && receivedByAudit) {
        return 'new-vouchers';
      }

      // PENDING DISPATCH: Vouchers worked on and ready to dispatch
      if (status === 'AUDIT: PROCESSING' && department === 'AUDIT' && workStarted && !dispatched) {
        return 'pending-dispatch';
      }

      // PENDING DISPATCH: Original rejected vouchers that need to be dispatched back to Finance
      if (status === 'VOUCHER REJECTED' && department === 'AUDIT' && !dispatched && !voucher.isRejectionCopy) {
        return 'pending-dispatch';
      }

      // DISPATCHED: Vouchers that have been dispatched back
      if ((status === 'VOUCHER CERTIFIED' || status === 'VOUCHER REJECTED' || status === 'VOUCHER RETURNED') && dispatched) {
        return 'dispatched';
      }

      // REJECTED: Permanent records of rejected vouchers (copies only)
      if (status === 'VOUCHER REJECTED' && department === 'AUDIT' && voucher.isRejectionCopy) {
        return 'rejected';
      }

      // RETURNED: Permanent records of returned vouchers
      if (status === 'VOUCHER RETURNED' && department === 'AUDIT') {
        return 'returned';
      }
    } else {
      // Finance department logic
      if (originalDepartment === userDepartment || department === userDepartment) {
        if (status === 'PENDING SUBMISSION') return 'pending';
        if (status === 'PENDING RECEIPT' || status === 'AUDIT: PROCESSING' || status === 'RE-SUBMISSION') return 'processing';
        if (status === 'VOUCHER CERTIFIED') return 'certified';

        if (status === 'VOUCHER REJECTED') return 'rejected';

        if (status === 'VOUCHER RETURNED') return 'returned';
      }
    }

    return null;
  }

  /**
   * Filter vouchers for specific tab and department
   */
  static getVouchersForTab(
    vouchers: Voucher[], 
    tabName: string, 
    userDepartment: string
  ): Voucher[] {
    return vouchers.filter(voucher => {
      const voucherTab = this.getTabForVoucher(voucher, userDepartment);
      return voucherTab === tabName;
    });
  }

  /**
   * Get tab counts for dashboard
   */
  static getTabCounts(vouchers: Voucher[], userDepartment: string): TabCounts {
    const counts: TabCounts = {};

    // Initialize counts based on user department
    if (userDepartment === 'AUDIT') {
      counts['new-vouchers'] = 0;
      counts['pending-dispatch'] = 0;
      counts['dispatched'] = 0;
      counts['rejected'] = 0;
      counts['returned'] = 0;
    } else {
      counts['pending'] = 0;
      counts['processing'] = 0;
      counts['certified'] = 0;
      counts['rejected'] = 0;
      counts['returned'] = 0;
    }

    // Count vouchers for each tab
    vouchers.forEach(voucher => {
      const tab = this.getTabForVoucher(voucher, userDepartment);
      if (tab && counts.hasOwnProperty(tab)) {
        counts[tab]++;
      }
    });

    return counts;
  }

  /**
   * Get display name for tab
   */
  static getTabDisplayName(tabName: string): string {
    return this.TAB_DISPLAY_NAMES[tabName] || tabName;
  }

  /**
   * Get badge display name
   */
  static getBadgeDisplayName(badgeType: BadgeType): string {
    return this.BADGE_DISPLAY_NAMES[badgeType] || '';
  }

  /**
   * Get badge CSS classes
   */
  static getBadgeClasses(badgeType: BadgeType): string {
    const baseClasses = 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium';
    const colorClasses = this.BADGE_COLORS[badgeType] || '';
    return `${baseClasses} ${colorClasses}`.trim();
  }

  /**
   * Check if voucher should show badge
   */
  static shouldShowBadge(voucher: Voucher): boolean {
    return voucher.badge_type !== BadgeType.NONE;
  }

  /**
   * Get all available tabs for department
   */
  static getAvailableTabs(userDepartment: string): string[] {
    if (userDepartment === 'AUDIT') {
      return ['new-vouchers', 'pending-dispatch', 'dispatched', 'rejected', 'returned'];
    } else {
      return ['pending', 'processing', 'certified', 'rejected', 'returned'];
    }
  }

  /**
   * Check if voucher is editable based on workflow state
   */
  static isVoucherEditable(voucher: Voucher, userDepartment: string): boolean {
    const { workflow_state } = voucher;

    if (userDepartment === 'AUDIT') {
      return workflow_state === WorkflowState.AUDIT_NEW || 
             workflow_state === WorkflowState.AUDIT_NEW_RESUBMITTED ||
             workflow_state === WorkflowState.AUDIT_PENDING_DISPATCH;
    }

    // Finance users can edit pending vouchers
    return workflow_state === WorkflowState.FINANCE_PENDING;
  }

  /**
   * Check if voucher can be dispatched
   */
  static canDispatchVoucher(voucher: Voucher, userDepartment: string): boolean {
    if (userDepartment !== 'AUDIT') return false;

    return voucher.workflow_state === WorkflowState.AUDIT_PENDING_DISPATCH ||
           voucher.workflow_state === WorkflowState.AUDIT_PENDING_DISPATCH_REJECTED ||
           voucher.workflow_state === WorkflowState.AUDIT_PENDING_DISPATCH_RETURNED;
  }

  /**
   * Check if voucher can be resubmitted
   */
  static canResubmitVoucher(voucher: Voucher, userDepartment: string): boolean {
    if (userDepartment === 'AUDIT') return false;

    return voucher.workflow_state === WorkflowState.FINANCE_REJECTED ||
           voucher.workflow_state === WorkflowState.FINANCE_RETURNED;
  }

  /**
   * Get next possible actions for voucher
   */
  static getAvailableActions(voucher: Voucher, userDepartment: string): string[] {
    const actions: string[] = [];

    if (this.isVoucherEditable(voucher, userDepartment)) {
      actions.push('edit');
    }

    if (this.canDispatchVoucher(voucher, userDepartment)) {
      actions.push('dispatch');
    }

    if (this.canResubmitVoucher(voucher, userDepartment)) {
      actions.push('resubmit');
    }

    // Always allow viewing
    actions.push('view');

    return actions;
  }

  /**
   * Sort vouchers by priority (newest first, with special handling for badges)
   */
  static sortVouchers(vouchers: Voucher[]): Voucher[] {
    return vouchers.sort((a, b) => {
      // Priority order: RE_SUBMITTED > RETURNED > REJECTED > NONE
      const badgePriority = {
        [BadgeType.RE_SUBMITTED]: 4,
        [BadgeType.RETURNED]: 3,
        [BadgeType.REJECTED]: 2,
        [BadgeType.NONE]: 1
      };

      const aPriority = badgePriority[a.badge_type] || 1;
      const bPriority = badgePriority[b.badge_type] || 1;

      if (aPriority !== bPriority) {
        return bPriority - aPriority; // Higher priority first
      }

      // If same priority, sort by creation date (newest first)
      return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
    });
  }
}
