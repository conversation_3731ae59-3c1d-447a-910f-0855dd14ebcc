const mysql = require('mysql2/promise');
require('dotenv').config();

async function checkAuditUsers() {
  let connection;
  
  try {
    // Create connection
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || 'vms@2025@1989',
      database: process.env.DB_NAME || 'vms_production'
    });

    console.log('🔗 Connected to database');
    console.log('🔍 CHECKING AUDIT USERS - Debugging notification creation\n');

    // Check all users
    const [allUsers] = await connection.execute(`
      SELECT id, name, department, is_active 
      FROM users 
      ORDER BY department, name
    `);

    console.log('👥 ALL USERS:');
    allUsers.forEach((user, index) => {
      console.log(`${index + 1}. ${user.name} (${user.department}) - Active: ${user.is_active ? 'YES' : 'NO'}`);
    });

    // Check specifically for AUDIT users
    const [auditUsers] = await connection.execute(`
      SELECT id, name, department, is_active 
      FROM users 
      WHERE department = "AUDIT" AND is_active = 1
    `);

    console.log(`\n🔍 AUDIT USERS (Active):${auditUsers.length}`);
    if (auditUsers.length === 0) {
      console.log('❌ NO ACTIVE AUDIT USERS FOUND!');
      console.log('This means the server will try to create a notification with user_id = "AUDIT"');
      console.log('This might cause a foreign key constraint error if notifications table expects a valid user ID.');
    } else {
      auditUsers.forEach((user, index) => {
        console.log(`${index + 1}. ${user.name} (ID: ${user.id})`);
      });
    }

    // Check the notifications table structure
    const [notificationColumns] = await connection.execute(`
      DESCRIBE notifications
    `);

    console.log(`\n📋 NOTIFICATIONS TABLE STRUCTURE:`);
    notificationColumns.forEach(col => {
      console.log(`- ${col.Field}: ${col.Type} ${col.Null === 'NO' ? '(NOT NULL)' : '(NULLABLE)'} ${col.Key ? `[${col.Key}]` : ''}`);
    });

    // Check if there are any foreign key constraints on notifications.user_id
    const [constraints] = await connection.execute(`
      SELECT 
        CONSTRAINT_NAME,
        COLUMN_NAME,
        REFERENCED_TABLE_NAME,
        REFERENCED_COLUMN_NAME
      FROM information_schema.KEY_COLUMN_USAGE 
      WHERE TABLE_SCHEMA = ? 
        AND TABLE_NAME = 'notifications' 
        AND COLUMN_NAME = 'user_id'
        AND REFERENCED_TABLE_NAME IS NOT NULL
    `, [process.env.DB_NAME || 'vms_production']);

    if (constraints.length > 0) {
      console.log(`\n🔗 FOREIGN KEY CONSTRAINTS ON notifications.user_id:`);
      constraints.forEach(constraint => {
        console.log(`- ${constraint.CONSTRAINT_NAME}: user_id -> ${constraint.REFERENCED_TABLE_NAME}.${constraint.REFERENCED_COLUMN_NAME}`);
      });
      console.log(`\n⚠️  POTENTIAL ISSUE:`);
      console.log(`If there are no AUDIT users, the server tries to insert user_id = "AUDIT"`);
      console.log(`But the foreign key constraint expects a valid user ID from the users table.`);
      console.log(`This would cause the INSERT to fail and rollback the entire transaction!`);
    } else {
      console.log(`\n✅ No foreign key constraints on notifications.user_id`);
    }

  } catch (error) {
    console.error('❌ Error checking audit users:', error);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n🔌 Database connection closed');
    }
  }
}

// Run the check
checkAuditUsers();
