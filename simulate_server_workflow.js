const mysql = require('mysql2/promise');
const { v4: uuidv4 } = require('uuid');
require('dotenv').config();

async function simulateServerWorkflow() {
  let connection;
  
  try {
    // Create connection pool like the server
    const pool = mysql.createPool({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || 'vms@2025@1989',
      database: process.env.DB_NAME || 'vms_production',
      waitForConnections: true,
      connectionLimit: 10,
      queueLimit: 0
    });

    // Start transaction like the server
    connection = await pool.getConnection();
    await connection.beginTransaction();

    console.log('🔗 Connected to database with transaction');
    console.log('🧪 SIMULATING EXACT SERVER WORKFLOW\n');

    const voucherId = '1e1fe711-a4b6-42bf-92bc-1cc3b7fc717c'; // KAY voucher ID
    const userName = 'FELIX AYISI'; // Simulating user

    // Step 1: Get voucher with FOR UPDATE lock (like server)
    console.log('📋 Step 1: Getting voucher with FOR UPDATE lock...');
    const [vouchers] = await connection.query('SELECT * FROM vouchers WHERE id = ? AND deleted = FALSE FOR UPDATE', [voucherId]);
    
    if (vouchers.length === 0) {
      await connection.rollback();
      console.log('❌ Voucher not found');
      return;
    }

    const voucher = vouchers[0];
    console.log(`✅ Found voucher: ${voucher.voucher_id} (${voucher.claimant})`);
    console.log(`   Current Status: ${voucher.status}`);
    console.log(`   Rejected By: ${voucher.rejected_by}`);
    console.log(`   Rejection Time: ${voucher.rejection_time}`);

    // Step 2: Apply resubmission detection logic (like server)
    console.log('\n🔍 Step 2: Applying resubmission detection logic...');
    const isResubmission = (voucher.rejected_by && voucher.rejected_by.trim() !== '') ||
                          (voucher.rejection_time) ||
                          (voucher.status === 'VOUCHER REJECTED') ||
                          (voucher.status === 'VOUCHER RETURNED');

    const targetStatus = isResubmission ? 'RE-SUBMISSION' : 'PENDING RECEIPT';
    const targetWorkflowState = isResubmission ? 'FINANCE_RESUBMISSION' : 'FINANCE_PROCESSING';

    console.log(`   isResubmission: ${isResubmission}`);
    console.log(`   targetStatus: ${targetStatus}`);
    console.log(`   targetWorkflowState: ${targetWorkflowState}`);

    // Step 3: Create batch (like server)
    console.log('\n📦 Step 3: Creating batch...');
    const batchId = uuidv4();
    const resubmissionCount = isResubmission ? 1 : 0;
    
    await connection.query(`INSERT INTO voucher_batches (
      id, department, sent_by, sent_time, received, contains_resubmissions, resubmission_count
    ) VALUES (?, ?, ?, NOW(), FALSE, ?, ?)`, [
      batchId, 'AUDIT', userName, isResubmission ? 1 : 0, resubmissionCount
    ]);
    
    console.log(`✅ Created batch: ${batchId}`);
    console.log(`   Contains Resubmissions: ${isResubmission ? 'YES' : 'NO'}`);
    console.log(`   Resubmission Count: ${resubmissionCount}`);

    // Step 4: Link voucher to batch (like server)
    console.log('\n🔗 Step 4: Linking voucher to batch...');
    await connection.query(`INSERT INTO batch_vouchers (batch_id, voucher_id) VALUES (?, ?)`, [batchId, voucher.id]);
    console.log(`✅ Linked voucher to batch`);

    // Step 5: Update voucher (like server) - THIS IS THE CRITICAL STEP
    console.log('\n🔄 Step 5: Updating voucher (CRITICAL STEP)...');
    const flags = {}; // Empty flags like server
    
    const updateQuery = `UPDATE vouchers 
      SET status = ?, 
          workflow_state = ?, 
          batch_id = ?, 
          flags = ?, 
          dispatch_to_audit_by = ?, 
          dispatch_time = NOW(), 
          sent_to_audit = TRUE, 
          is_resubmitted = ?, 
          reference_id = ? 
      WHERE id = ?`;
    
    const updateParams = [
      targetStatus, 
      targetWorkflowState, 
      batchId, 
      JSON.stringify(flags), 
      userName, 
      isResubmission ? 1 : 0, 
      voucher.voucher_id, 
      voucherId
    ];

    console.log(`   Query: ${updateQuery}`);
    console.log(`   Params: [${updateParams.join(', ')}]`);

    const [updateResult] = await connection.query(updateQuery, updateParams);
    console.log(`✅ Update Result:`);
    console.log(`   Affected Rows: ${updateResult.affectedRows}`);
    console.log(`   Changed Rows: ${updateResult.changedRows}`);

    // Step 6: Create notifications (like server)
    console.log('\n📢 Step 6: Creating notifications...');
    const notificationId = uuidv4();
    const [auditUsers] = await connection.query('SELECT id FROM users WHERE department = "AUDIT" AND is_active = 1');
    
    const notificationMessage = isResubmission
      ? `Re-submitted voucher ${voucher.voucher_id} received from ${voucher.department}`
      : `New voucher ${voucher.voucher_id} received from ${voucher.department}`;
    const notificationType = isResubmission ? 'RESUBMISSION' : 'NEW_VOUCHER';

    if (auditUsers.length === 0) {
      await connection.query(`INSERT INTO notifications (
        id, user_id, message, is_read, timestamp, voucher_id, type
      ) VALUES (?, ?, ?, ?, NOW(), ?, ?)`, [
        notificationId, 'AUDIT', notificationMessage, false, voucherId, notificationType
      ]);
      console.log(`✅ Created notification for AUDIT department`);
    } else {
      for (const auditUser of auditUsers) {
        await connection.query(`INSERT INTO notifications (
          id, user_id, message, is_read, timestamp, voucher_id, type
        ) VALUES (?, ?, ?, ?, NOW(), ?, ?)`, [
          uuidv4(), auditUser.id, notificationMessage, false, voucherId, notificationType
        ]);
      }
      console.log(`✅ Created notifications for ${auditUsers.length} audit users`);
    }

    // Step 7: Get updated voucher (like server)
    console.log('\n📋 Step 7: Getting updated voucher...');
    const [updatedVouchers] = await connection.query('SELECT * FROM vouchers WHERE id = ?', [voucherId]);
    const updatedVoucher = updatedVouchers[0];
    
    console.log(`✅ Retrieved updated voucher:`);
    console.log(`   Status: ${updatedVoucher.status}`);
    console.log(`   Workflow State: ${updatedVoucher.workflow_state}`);
    console.log(`   Is Resubmitted: ${updatedVoucher.is_resubmitted}`);
    console.log(`   Batch ID: ${updatedVoucher.batch_id}`);

    // Step 8: COMMIT TRANSACTION (like server)
    console.log('\n💾 Step 8: COMMITTING TRANSACTION...');
    await connection.commit();
    console.log(`✅ Transaction committed successfully!`);

    // Step 9: Verify final state
    console.log('\n🔍 Step 9: Verifying final state...');
    const [finalVouchers] = await connection.query('SELECT * FROM vouchers WHERE id = ?', [voucherId]);
    const finalVoucher = finalVouchers[0];
    
    console.log(`📊 FINAL VOUCHER STATE:`);
    console.log(`   Status: ${finalVoucher.status} (Expected: ${targetStatus}) ${finalVoucher.status === targetStatus ? '✅' : '❌'}`);
    console.log(`   Workflow State: ${finalVoucher.workflow_state} (Expected: ${targetWorkflowState}) ${finalVoucher.workflow_state === targetWorkflowState ? '✅' : '❌'}`);
    console.log(`   Is Resubmitted: ${finalVoucher.is_resubmitted} (Expected: ${isResubmission ? 1 : 0}) ${finalVoucher.is_resubmitted === (isResubmission ? 1 : 0) ? '✅' : '❌'}`);

    const [finalBatches] = await connection.query('SELECT * FROM voucher_batches WHERE id = ?', [batchId]);
    const finalBatch = finalBatches[0];
    
    console.log(`📦 FINAL BATCH STATE:`);
    console.log(`   Contains Resubmissions: ${finalBatch.contains_resubmissions} (Expected: ${isResubmission ? 1 : 0}) ${finalBatch.contains_resubmissions === (isResubmission ? 1 : 0) ? '✅' : '❌'}`);
    console.log(`   Resubmission Count: ${finalBatch.resubmission_count} (Expected: ${resubmissionCount}) ${finalBatch.resubmission_count === resubmissionCount ? '✅' : '❌'}`);

    if (finalVoucher.status === targetStatus && 
        finalVoucher.workflow_state === targetWorkflowState && 
        finalVoucher.is_resubmitted === (isResubmission ? 1 : 0) &&
        finalBatch.contains_resubmissions === (isResubmission ? 1 : 0)) {
      console.log(`\n🎉 SIMULATION SUCCESSFUL!`);
      console.log(`The server workflow logic works perfectly when executed manually.`);
      console.log(`The issue must be in the actual server execution or error handling.`);
    } else {
      console.log(`\n❌ SIMULATION FAILED!`);
      console.log(`There's an issue with the workflow logic or database constraints.`);
    }

    pool.end();

  } catch (error) {
    console.error('\n❌ Error during simulation:', error);
    if (connection) {
      await connection.rollback();
      console.log('🔄 Transaction rolled back due to error');
    }
    process.exit(1);
  }
}

// Run the simulation
simulateServerWorkflow();
