const mysql = require('mysql2/promise');

async function checkFinanceReceivedField() {
  console.log('🔍 CHECKING FINANCE_RECEIVED FIELD');
  console.log('==================================');
  
  const connection = await mysql.createConnection({
    host: 'localhost',
    user: 'root',
    password: 'vms@2025@1989',
    database: 'vms_production'
  });
  
  try {
    // Check if finance_received field exists
    const [columns] = await connection.execute(`
      SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = 'vms_production' 
      AND TABLE_NAME = 'vouchers' 
      AND COLUMN_NAME = 'finance_received'
    `);
    
    if (columns.length > 0) {
      console.log('✅ finance_received field exists:');
      console.log('   Column:', columns[0].COLUMN_NAME);
      console.log('   Type:', columns[0].DATA_TYPE);
      console.log('   Nullable:', columns[0].IS_NULLABLE);
      console.log('   Default:', columns[0].COLUMN_DEFAULT);
    } else {
      console.log('❌ finance_received field does NOT exist');
      console.log('🔧 Adding finance_received field...');
      
      await connection.execute(`
        ALTER TABLE vouchers 
        ADD COLUMN finance_received TINYINT(1) DEFAULT 0 
        COMMENT 'Track if Finance has received the voucher from Audit'
      `);
      
      console.log('✅ finance_received field added successfully');
    }
    
  } catch (error) {
    console.error('❌ Error checking/adding finance_received field:', error);
  } finally {
    await connection.end();
  }
}

checkFinanceReceivedField().catch(console.error);
