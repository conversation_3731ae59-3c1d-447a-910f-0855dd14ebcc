const mysql = require('mysql2/promise');
require('dotenv').config();

async function debugResubmission() {
  let connection;
  
  try {
    // Create connection
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || 'vms@2025@1989',
      database: process.env.DB_NAME || 'vms_production'
    });

    console.log('🔗 Connected to database');
    console.log('🔍 Debugging KIM voucher resubmission detection...\n');

    // Get the exact voucher state that the server would see
    const [vouchers] = await connection.execute(`
      SELECT 
        id,
        voucher_id, 
        claimant, 
        status, 
        workflow_state, 
        is_resubmitted,
        sent_to_audit,
        batch_id,
        rejected_by,
        rejection_time,
        comment
      FROM vouchers 
      WHERE claimant = 'KIM' AND voucher_id = 'FINJUL0004'
    `);

    if (vouchers.length === 0) {
      console.log('❌ No KIM voucher found');
      return;
    }

    const voucher = vouchers[0];
    console.log('📊 KIM Voucher Details:');
    console.log(`ID: ${voucher.id}`);
    console.log(`Voucher ID: ${voucher.voucher_id}`);
    console.log(`Status: ${voucher.status}`);
    console.log(`Workflow State: ${voucher.workflow_state}`);
    console.log(`Is Resubmitted: ${voucher.is_resubmitted ? 'YES' : 'NO'}`);
    console.log(`Sent to Audit: ${voucher.sent_to_audit ? 'YES' : 'NO'}`);
    console.log(`Batch ID: ${voucher.batch_id}`);
    console.log(`Rejected By: ${voucher.rejected_by || 'None'}`);
    console.log(`Rejection Time: ${voucher.rejection_time || 'None'}`);
    console.log(`Comment: ${voucher.comment || 'None'}`);

    // Test the resubmission detection logic
    console.log('\n🧪 Testing Resubmission Detection Logic:');
    
    const condition1 = voucher.rejected_by && voucher.rejected_by.trim() !== '';
    const condition2 = voucher.rejection_time;
    const condition3 = voucher.status === 'VOUCHER REJECTED';
    const condition4 = voucher.status === 'VOUCHER RETURNED';
    
    console.log(`Condition 1 (has rejected_by): ${condition1} (${voucher.rejected_by})`);
    console.log(`Condition 2 (has rejection_time): ${condition2} (${voucher.rejection_time})`);
    console.log(`Condition 3 (status = VOUCHER REJECTED): ${condition3}`);
    console.log(`Condition 4 (status = VOUCHER RETURNED): ${condition4}`);
    
    const isResubmission = condition1 || condition2 || condition3 || condition4;
    console.log(`\n🎯 FINAL RESULT: isResubmission = ${isResubmission}`);
    
    if (isResubmission) {
      console.log('✅ This voucher SHOULD be detected as a resubmission');
      console.log('✅ Status should be set to RE-SUBMISSION');
      console.log('✅ Batch should have contains_resubmissions = true');
    } else {
      console.log('❌ This voucher would NOT be detected as a resubmission');
      console.log('❌ This is the bug - the detection logic is failing');
    }

    // Check the batch that contains this voucher
    if (voucher.batch_id) {
      console.log('\n📦 Checking Batch Details:');
      const [batches] = await connection.execute(`
        SELECT 
          id,
          department,
          sent_by,
          sent_time,
          received,
          contains_resubmissions,
          resubmission_count
        FROM voucher_batches 
        WHERE id = ?
      `, [voucher.batch_id]);

      if (batches.length > 0) {
        const batch = batches[0];
        console.log(`Batch ID: ${batch.id}`);
        console.log(`Department: ${batch.department}`);
        console.log(`Sent By: ${batch.sent_by}`);
        console.log(`Contains Resubmissions: ${batch.contains_resubmissions ? 'YES' : 'NO'}`);
        console.log(`Resubmission Count: ${batch.resubmission_count || 0}`);
        
        if (isResubmission && !batch.contains_resubmissions) {
          console.log('\n❌ BATCH BUG CONFIRMED:');
          console.log('   - Voucher IS a resubmission');
          console.log('   - But batch contains_resubmissions = false');
          console.log('   - This means the server logic failed during batch creation');
        }
      }
    }

    console.log('\n🔧 DIAGNOSIS:');
    if (isResubmission) {
      console.log('✅ Resubmission detection logic is working correctly');
      console.log('❌ But the batch creation logic is not applying it properly');
      console.log('🎯 Need to check the server send-to-audit endpoint');
    } else {
      console.log('❌ Resubmission detection logic is broken');
      console.log('🎯 Need to fix the detection conditions');
    }

  } catch (error) {
    console.error('❌ Error debugging resubmission:', error);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n🔌 Database connection closed');
    }
  }
}

// Run the debug
debugResubmission();
