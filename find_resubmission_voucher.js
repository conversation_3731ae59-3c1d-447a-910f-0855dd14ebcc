const mysql = require('mysql2/promise');

async function findResubmissionVoucher() {
  console.log('🔍 FINDING ACTUAL RESUBMISSION VOUCHERS');
  console.log('=======================================');
  
  const connection = await mysql.createConnection({
    host: 'localhost',
    user: 'root',
    password: 'vms@2025@1989',
    database: 'vms_production'
  });
  
  try {
    // Look for any vouchers with is_resubmitted = 1
    const [resubmissions] = await connection.execute(`
      SELECT id, voucher_id, status, workflow_state, department, original_department,
             is_resubmitted, rejected_by, rejection_time, comment, certified_by,
             received_by, department_received_by, finance_received, dispatched_by,
             audit_dispatched_by, batch_id, created_at
      FROM vouchers 
      WHERE is_resubmitted = 1
      ORDER BY created_at DESC
      LIMIT 10
    `);
    
    console.log(`📊 Found ${resubmissions.length} resubmission voucher(s):`);
    
    if (resubmissions.length === 0) {
      console.log('❌ NO RESUBMISSION VOUCHERS FOUND!');
      console.log('');
      console.log('🔍 This explains the issue:');
      console.log('   - FINJUL0002 was rejected but never resubmitted');
      console.log('   - The "Add Back" button was never clicked');
      console.log('   - No resubmission workflow was actually tested');
      console.log('');
      console.log('🎯 TO TEST RESUBMISSION WORKFLOW:');
      console.log('   1. Go to Finance REJECTED tab');
      console.log('   2. Find FINJUL0002 voucher');
      console.log('   3. Click "Add Back" button');
      console.log('   4. This will set is_resubmitted = 1');
      console.log('   5. Then send it through the workflow again');
      
      // Let's check what vouchers are in REJECTED state that could be resubmitted
      const [rejectedVouchers] = await connection.execute(`
        SELECT voucher_id, status, workflow_state, department, rejected_by, comment
        FROM vouchers 
        WHERE status = 'VOUCHER REJECTED' AND workflow_state = 'FINANCE_REJECTED'
        ORDER BY created_at DESC
        LIMIT 5
      `);
      
      console.log(`\n📋 Available vouchers for resubmission (${rejectedVouchers.length}):`);
      rejectedVouchers.forEach((v, i) => {
        console.log(`   ${i+1}. ${v.voucher_id} - ${v.status} - ${v.workflow_state}`);
        console.log(`      Rejected by: ${v.rejected_by}`);
        console.log(`      Reason: ${v.comment || 'No comment'}`);
      });
      
      return;
    }
    
    resubmissions.forEach((voucher, index) => {
      console.log(`\n📋 RESUBMISSION VOUCHER ${index + 1}:`);
      console.log('====================================');
      console.log('   id:', voucher.id);
      console.log('   voucher_id:', voucher.voucher_id);
      console.log('   status:', voucher.status);
      console.log('   workflow_state:', voucher.workflow_state);
      console.log('   department:', voucher.department);
      console.log('   original_department:', voucher.original_department);
      console.log('   is_resubmitted:', voucher.is_resubmitted);
      console.log('   rejected_by:', voucher.rejected_by);
      console.log('   rejection_time:', voucher.rejection_time);
      console.log('   comment:', voucher.comment);
      console.log('   certified_by:', voucher.certified_by);
      console.log('   received_by:', voucher.received_by);
      console.log('   department_received_by:', voucher.department_received_by);
      console.log('   finance_received:', voucher.finance_received);
      console.log('   dispatched_by:', voucher.dispatched_by);
      console.log('   audit_dispatched_by:', voucher.audit_dispatched_by);
      console.log('   batch_id:', voucher.batch_id);
      console.log('   created_at:', voucher.created_at);
      
      // Analyze what tab this should appear in
      console.log('\n🔍 TAB ANALYSIS:');
      console.log('================');
      
      // Finance CERTIFIED tab logic
      const financeTabLogic = voucher.workflow_state === 'AUDIT_DISPATCHED' && 
                             voucher.status === 'VOUCHER CERTIFIED' && 
                             voucher.finance_received === 1 &&
                             voucher.department === 'FINANCE';
      
      console.log('   Finance CERTIFIED Tab Logic:');
      console.log(`     workflow_state === 'AUDIT_DISPATCHED': ${voucher.workflow_state === 'AUDIT_DISPATCHED'}`);
      console.log(`     status === 'VOUCHER CERTIFIED': ${voucher.status === 'VOUCHER CERTIFIED'}`);
      console.log(`     finance_received === 1: ${voucher.finance_received === 1}`);
      console.log(`     department === 'FINANCE': ${voucher.department === 'FINANCE'}`);
      console.log(`     ✅ Should appear in Finance CERTIFIED: ${financeTabLogic}`);
      
      // Audit DISPATCHED tab logic
      const auditTabLogic = voucher.workflow_state === 'AUDIT_DISPATCHED' && 
                           voucher.audit_dispatched_by;
      
      console.log('\n   Audit DISPATCHED Tab Logic:');
      console.log(`     workflow_state === 'AUDIT_DISPATCHED': ${voucher.workflow_state === 'AUDIT_DISPATCHED'}`);
      console.log(`     audit_dispatched_by exists: ${!!voucher.audit_dispatched_by}`);
      console.log(`     ✅ Should appear in Audit DISPATCHED: ${auditTabLogic}`);
      
      // Badge logic
      const badgeLogic = voucher.is_resubmitted === 1;
      const isCertified = voucher.certified_by || voucher.department_received_by;
      const expectedBadge = badgeLogic ? (isCertified ? 'CERTIFIED-RESUBMISSION' : 'RESUBMISSION') : 'NONE';
      
      console.log('\n   Badge Logic:');
      console.log(`     is_resubmitted === 1: ${voucher.is_resubmitted === 1}`);
      console.log(`     certified_by or department_received_by exists: ${!!isCertified}`);
      console.log(`     ✅ Expected Badge: ${expectedBadge}`);
    });
    
  } catch (error) {
    console.error('❌ Error finding resubmission vouchers:', error);
  } finally {
    await connection.end();
  }
}

findResubmissionVoucher().catch(console.error);
