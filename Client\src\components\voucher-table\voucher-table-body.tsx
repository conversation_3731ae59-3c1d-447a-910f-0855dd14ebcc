import { Eye, RefreshCw, Trash2 } from 'lucide-react';
import { formatNumberWithCommas, formatVMSDateTime } from '@/utils/formatUtils';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Button } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableRow,
  TableCell
} from '@/components/ui/table';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Voucher, TransactionStatus } from '@/lib/types';

interface VoucherTableBodyProps {
  filteredVouchers: Voucher[];
  selectable: boolean;
  selectedVouchers: string[];
  handleSelectVoucher: (voucherId: string) => void;
  handleViewVoucher: (voucher: Voucher) => void;
  handleAddBack: (voucher: Voucher) => void;
  handleDeleteVoucher: (voucherId: string) => void;
  view: string;
  isAudit: boolean;
  showAddBack: boolean;
  showDelete: boolean;
  showPreAuditedBy: boolean;
}

export function VoucherTableBody({
  filteredVouchers,
  selectable,
  selectedVouchers,
  handleSelectVoucher,
  handleViewVoucher,
  handleAddBack,
  handleDeleteVoucher,
  view,
  isAudit,
  showAddBack,
  showDelete,
  showPreAuditedBy
}: VoucherTableBodyProps) {
  const getStatusBadge = (status: TransactionStatus) => {
    switch (status) {
      case 'VOUCHER CERTIFIED':
        return <Badge className="bg-green-500 text-white">CERTIFIED</Badge>;
      case 'VOUCHER REJECTED':
        return <Badge className="bg-red-500 text-white">REJECTED</Badge>;
      case 'AUDIT: PROCESSING':
        return <Badge className="bg-yellow-500 text-black">RECEIVED: PROCESSING</Badge>;
      case 'PENDING RECEIPT':
        return <Badge className="bg-yellow-500 text-black">PENDING RECEIPT</Badge>;
      case 'RE-SUBMISSION':
        return <Badge className="bg-blue-500 text-white">RE-SUBMISSION</Badge>;
      case 'PENDING SUBMISSION':
        return <Badge className="border border-gray-200">PENDING SUBMISSION</Badge>;
      case 'VOUCHER RETURNED':
        return <Badge className="bg-gray-500 text-white">RETURNED</Badge>;
      default:
        return <Badge className="border border-gray-200">{status}</Badge>;
    }
  };

  // RESUBMISSION FIX: Function to get resubmission badge
  const getResubmissionBadge = (voucher: Voucher) => {
    if (voucher.isResubmission || voucher.status === 'RE-SUBMISSION') {
      return <Badge className="bg-purple-500 text-white ml-2">RE-SUBMITTED</Badge>;
    }
    return null;
  };

  const isVoucherSelectable = (voucher: Voucher): boolean => {
    return (voucher.status === "PENDING" || voucher.status === "PENDING SUBMISSION") && !voucher.sentToAudit;
  };

  // Helper function to get comment text safely
  const getCommentText = (voucher: Voucher): string => {
    // Debug log to inspect comment values
    console.log(`Getting comment for voucher ${voucher.id}:`, {
      returnComment: voucher.returnComment,
      comment: voucher.comment,
      isReturned: voucher.isReturned,
      pendingReturn: voucher.pendingReturn,
      status: voucher.status
    });

    // For returned or pending return vouchers, show only the return comment
    if (voucher.pendingReturn || voucher.isReturned || voucher.status === "VOUCHER RETURNED") {
      return String(voucher.returnComment || "NO COMMENT PROVIDED");
    }

    // For non-returned vouchers, show regular comment
    return String(voucher.comment || "NO COMMENT PROVIDED");
  };

  return (
    <div className="w-full">
      <table className="w-full table-fixed" style={{ tableLayout: 'fixed' }}>
        <tbody>
        {filteredVouchers.length === 0 ? (
          <tr className="border-b h-14">
            <td colSpan={selectable ? 9 : 8} className="p-4 text-center uppercase font-medium">
              No vouchers found.
            </td>
          </tr>
        ) : (
          filteredVouchers.map((voucher) => {
            const isSelectableVoucher = selectable && isVoucherSelectable(voucher);

            return (
              <tr key={voucher.id} className="border-b h-14">
                {selectable && (
                  <td className="sticky left-0 bg-background z-10 p-4 align-middle w-[5%] text-center">
                    <Checkbox
                      checked={selectedVouchers.includes(voucher.id)}
                      onCheckedChange={() => handleSelectVoucher(voucher.id)}
                      aria-label={`Select voucher ${voucher.voucherId}`}
                      disabled={!isSelectableVoucher}
                    />
                  </td>
                )}
                <td className="font-medium uppercase sticky left-0 bg-background z-10 p-4 align-middle w-[15%] text-center">
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <span className="block truncate font-mono">
                          {voucher.voucherId || (voucher as any).voucher_id || 'N/A'}
                        </span>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>{voucher.voucherId || (voucher as any).voucher_id || 'N/A'}</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </td>
                <td className="uppercase p-4 align-middle w-[20%] text-center">
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <span className="block truncate">{formatVMSDateTime(voucher.date)}</span>
                      </TooltipTrigger>
                      <TooltipContent>
                        <div className="text-center">
                          <p className="font-semibold">Voucher Date</p>
                          <p>{formatVMSDateTime(voucher.date)}</p>
                          {voucher.createdAt && (
                            <>
                              <p className="font-semibold mt-2">Created</p>
                              <p>{formatVMSDateTime(voucher.createdAt)}</p>
                            </>
                          )}
                        </div>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </td>
                <td className="uppercase p-4 align-middle w-[20%] text-center">
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <span className="block truncate">{voucher.claimant}</span>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>{voucher.claimant}</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </td>
                <td className="max-w-xs truncate uppercase p-4 align-middle w-[25%] text-center">
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <span className="block truncate">{voucher.description}</span>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>{voucher.description}</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </td>
                <td className="uppercase p-4 align-middle w-[10%] text-center text-xs">
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <span className="block truncate">
                          {formatNumberWithCommas(voucher.amount)} {voucher.currency}
                        </span>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>{formatNumberWithCommas(voucher.amount)} {voucher.currency}</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </td>

                {!isAudit && view === 'certified' && (
                  <td className="uppercase p-4 align-middle w-[15%] text-center text-xs">
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <span className="block truncate">
                            {voucher.preAuditedAmount ? `${formatNumberWithCommas(voucher.preAuditedAmount)} ${voucher.currency}` : '-'}
                          </span>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>{voucher.preAuditedAmount ? `${formatNumberWithCommas(voucher.preAuditedAmount)} ${voucher.currency}` : '-'}</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </td>
                )}

                {isAudit && showPreAuditedBy && (
                  <>
                    <td className="uppercase p-4 align-middle w-[15%] text-center">
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <span className="block truncate">
                              {voucher.preAuditedAmount ? `${formatNumberWithCommas(voucher.preAuditedAmount)} ${voucher.currency}` : '-'}
                            </span>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>{voucher.preAuditedAmount ? `${formatNumberWithCommas(voucher.preAuditedAmount)} ${voucher.currency}` : '-'}</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </td>
                    <td className="uppercase p-4 align-middle w-[15%] text-center">
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <span className="block truncate">{voucher.preAuditedBy || '-'}</span>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>{voucher.preAuditedBy || '-'}</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </td>
                  </>
                )}

                {view === 'dispatched' && (
                  <>
                    <td className="uppercase p-4 align-middle w-[15%] text-center">
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <span className="block truncate">{voucher.certifiedBy || '-'}</span>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>{voucher.certifiedBy || '-'}</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </td>
                    <td className="uppercase p-4 align-middle w-[15%] text-center text-xs">
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <span className="block truncate">
                              {voucher.taxType && voucher.taxAmount ?
                                `${voucher.taxType}: ${formatNumberWithCommas(voucher.taxAmount)} ${voucher.currency}` :
                                (voucher.taxType || '-')}
                            </span>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>
                              {voucher.taxType && voucher.taxAmount ?
                                `${voucher.taxType}: ${formatNumberWithCommas(voucher.taxAmount)} ${voucher.currency}` :
                                (voucher.taxType || '-')}
                            </p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </td>
                    <td className="uppercase p-4 align-middle w-[15%] text-center">
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <span className="block truncate">{voucher.auditDispatchedBy || '-'}</span>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>{voucher.auditDispatchedBy || '-'}</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </td>
                    <td className="uppercase p-4 align-middle w-[15%] text-center">
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <span className="block truncate">
                              {voucher.auditDispatchTime ? formatVMSDateTime(voucher.auditDispatchTime) : '-'}
                            </span>
                          </TooltipTrigger>
                          <TooltipContent>
                            <div className="text-center">
                              <p className="font-semibold">Audit Dispatch Time</p>
                              <p>{voucher.auditDispatchTime ? formatVMSDateTime(voucher.auditDispatchTime) : 'Not dispatched'}</p>
                              {voucher.auditDispatchedBy && (
                                <>
                                  <p className="font-semibold mt-2">Dispatched By</p>
                                  <p>{voucher.auditDispatchedBy}</p>
                                </>
                              )}
                            </div>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </td>
                    <td className="uppercase p-4 align-middle w-[15%] text-center">
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <span className="block truncate">
                              {voucher.receivedBy ?
                                `${voucher.receivedBy}${voucher.departmentReceiptTime ? ` AT ${formatVMSTime(voucher.departmentReceiptTime)}` : ''}` :
                                'PENDING'}
                            </span>
                          </TooltipTrigger>
                          <TooltipContent>
                            <div className="text-center">
                              <p className="font-semibold">Department Receipt</p>
                              {voucher.receivedBy ? (
                                <>
                                  <p className="font-semibold mt-2">Received By</p>
                                  <p>{voucher.receivedBy}</p>
                                  {voucher.departmentReceiptTime && (
                                    <>
                                      <p className="font-semibold mt-2">Receipt Time</p>
                                      <p>{formatVMSDateTime(voucher.departmentReceiptTime)}</p>
                                    </>
                                  )}
                                </>
                              ) : (
                                <p>Pending receipt</p>
                              )}
                            </div>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </td>
                  </>
                )}

                {view === 'returned' && (
                  <>
                    <td className="uppercase p-4 align-middle w-[15%] text-center">
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <span className="block truncate">
                              {voucher.returnTime ? formatVMSDateTime(voucher.returnTime) : '-'}
                            </span>
                          </TooltipTrigger>
                          <TooltipContent>
                            <div className="text-center">
                              <p className="font-semibold">Return Time</p>
                              <p>{voucher.returnTime ? formatVMSDateTime(voucher.returnTime) : 'Not returned'}</p>
                            </div>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </td>
                    <td className="uppercase p-4 align-middle w-[15%] text-center">
                      <div className="flex items-center justify-center">
                        {getStatusBadge(voucher.status)}
                        {getResubmissionBadge(voucher)}
                      </div>
                    </td>
                    <td className="uppercase max-w-[250px] truncate p-4 align-middle w-[25%] text-center">
                      {getCommentText(voucher)}
                    </td>
                  </>
                )}

                {view === 'rejected' && (
                  <td className="uppercase p-4 align-middle w-[15%] text-center">
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <span className="block truncate">
                            {voucher.rejectionTime ? formatVMSDateTime(voucher.rejectionTime) : '-'}
                          </span>
                        </TooltipTrigger>
                        <TooltipContent>
                          <div className="text-center">
                            <p className="font-semibold">Rejection Time</p>
                            <p>{voucher.rejectionTime ? formatVMSDateTime(voucher.rejectionTime) : 'Not rejected'}</p>
                            {voucher.rejectedBy && (
                              <>
                                <p className="font-semibold mt-2">Rejected By</p>
                                <p>{voucher.rejectedBy}</p>
                              </>
                            )}
                          </div>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </td>
                )}

                {isAudit && view === 'rejected' && (
                  <>
                    <td className="uppercase p-4 align-middle w-[15%] text-center">{voucher.dispatchedBy || '-'}</td>
                    <td className="uppercase p-4 align-middle w-[15%] text-center">{voucher.dispatchToAuditBy || '-'}</td>
                    <td className="uppercase p-4 align-middle w-[15%] text-center">{voucher.rejectedBy || '-'}</td>
                  </>
                )}

                {view !== 'rejected' && view !== 'returned' && (
                  <td className="p-4 align-middle w-[15%] text-center">
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <div className="flex items-center justify-center">
                            {getStatusBadge(voucher.status)}
                            {getResubmissionBadge(voucher)}
                          </div>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>{voucher.status}</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </td>
                )}

                <td className="text-center sticky right-0 bg-background z-10 p-4 align-middle w-[10%]">
                  <div className="flex justify-center gap-2">
                    <Button
                      className="hover:bg-gray-100 h-8 w-8 p-0 bg-blue-50"
                      onClick={(e) => {
                        e.stopPropagation();
                        console.log('Eye button clicked for voucher:', voucher.id);
                        handleViewVoucher(voucher);
                      }}
                      title="View Voucher Details"
                    >
                      <Eye className="h-4 w-4 text-blue-600" />
                    </Button>

                    {showAddBack && (
                      <Button
                        className="hover:bg-gray-100 h-8 w-8 p-0"
                        onClick={() => handleAddBack(voucher)}
                        title="Add Back to Pending Submission"
                      >
                        <RefreshCw className="h-4 w-4" />
                      </Button>
                    )}

                    {(showDelete || (view === 'pending-submission' && !voucher.sentToAudit)) && (
                      <Button
                        className="text-red-500 hover:text-red-700 hover:bg-gray-100 h-8 w-8 p-0"
                        onClick={() => handleDeleteVoucher(voucher.id)}
                        title="Delete Voucher"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                </td>
              </tr>
            );
          })
        )}
        {/* Add extra padding space at the bottom */}
        <tr className="border-b">
          <td colSpan={12} className="h-24 py-8"></td>
        </tr>
        </tbody>
      </table>
    </div>
  );
}
