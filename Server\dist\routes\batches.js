"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.batchRouter = void 0;
const express_1 = __importDefault(require("express"));
const uuid_1 = require("uuid");
const db_js_1 = require("../database/db.js");
const auth_js_1 = require("../middleware/auth.js");
const logger_js_1 = require("../utils/logger.js");
const legacy_voucher_status_compat_js_1 = require("../utils/legacy-voucher-status-compat.js");
const simpleEventBus_js_1 = require("../events/simpleEventBus.js");
exports.batchRouter = express_1.default.Router();
// Apply authentication middleware to all routes
exports.batchRouter.use(auth_js_1.authenticate);
// Get all batches
exports.batchRouter.get('/', async (req, res) => {
    try {
        const { department } = req.query;
        let batches;
        if (department) {
            // If department is specified, filter by department
            batches = await (0, db_js_1.query)('SELECT * FROM voucher_batches WHERE department = ?', [department]);
        }
        else if (req.user.department === 'AUDIT' || req.user.department === 'SYSTEM ADMIN') {
            // CRITICAL FIX: Audit should only see batches FROM departments TO audit
            // Batches FROM audit TO departments should appear in department dashboards
            batches = await (0, db_js_1.query)('SELECT * FROM voucher_batches WHERE from_audit = 0');
        }
        else {
            // Other departments see only their batches
            batches = await (0, db_js_1.query)('SELECT * FROM voucher_batches WHERE department = ?', [req.user.department]);
        }
        // Get vouchers for each batch
        for (const batch of batches) {
            const batchVouchers = await (0, db_js_1.query)(`SELECT v.* FROM vouchers v
         JOIN batch_vouchers bv ON v.id = bv.voucher_id
         WHERE bv.batch_id = ?`, [batch.id]);
            batch.vouchers = batchVouchers;
            batch.voucherIds = batchVouchers.map((v) => v.id);
        }
        // CRITICAL FIX: Transform database field names to match frontend expectations
        const transformedBatches = batches.map(batch => ({
            ...batch,
            fromAudit: batch.from_audit, // Transform snake_case to camelCase
            sentBy: batch.sent_by,
            sentTime: batch.sent_time
        }));
        res.json(transformedBatches);
    }
    catch (error) {
        logger_js_1.logger.error('Get batches error:', error);
        res.status(500).json({ error: 'Failed to get batches' });
    }
});
// Get batch by ID
exports.batchRouter.get('/:id', async (req, res) => {
    const batchId = req.params.id;
    try {
        logger_js_1.logger.info(`Fetching batch: ${batchId}`);
        // PRODUCTION FIX: Validate batch ID format
        if (!batchId || batchId.trim() === '') {
            logger_js_1.logger.warn('Invalid batch ID provided');
            return res.status(400).json({ error: 'Invalid batch ID provided' });
        }
        // Get batch with timeout protection
        logger_js_1.logger.info('Step 1: Fetching batch from database');
        const batches = await (0, db_js_1.query)('SELECT * FROM voucher_batches WHERE id = ?', [batchId]);
        if (batches.length === 0) {
            logger_js_1.logger.warn(`Batch not found: ${batchId}`);
            return res.status(404).json({
                error: 'Batch not found',
                message: `Batch ${batchId} does not exist or has been removed`,
                batchId: batchId
            });
        }
        const batch = batches[0];
        logger_js_1.logger.info(`Step 2: Batch found - ${batch.department} from ${batch.sent_by}`);
        // Check if user has access to this batch
        if (req.user.department !== 'AUDIT' && req.user.department !== 'SYSTEM ADMIN' && batch.department !== req.user.department) {
            logger_js_1.logger.warn(`Access denied for user ${req.user.name} to batch ${batchId}`);
            return res.status(403).json({ error: 'Access denied' });
        }
        // Get vouchers in this batch with timeout protection
        logger_js_1.logger.info('Step 3: Fetching vouchers in batch');
        const batchVouchers = await (0, db_js_1.query)(`SELECT v.* FROM vouchers v
       JOIN batch_vouchers bv ON v.id = bv.voucher_id
       WHERE bv.batch_id = ?`, [batchId]);
        logger_js_1.logger.info(`Step 4: Found ${batchVouchers.length} vouchers in batch`);
        batch.vouchers = batchVouchers;
        batch.voucherIds = batchVouchers.map((v) => v.id);
        // PRODUCTION FIX: Add additional metadata for better error handling
        batch.voucherCount = batchVouchers.length;
        batch.hasVouchers = batchVouchers.length > 0;
        logger_js_1.logger.info(`Batch ${batchId} fetched successfully with ${batchVouchers.length} vouchers`);
        res.json(batch);
    }
    catch (error) {
        logger_js_1.logger.error('Get batch error:', error);
        res.status(500).json({
            error: 'Failed to get batch',
            message: 'An internal server error occurred while fetching the batch',
            batchId: batchId
        });
    }
});
// Create batch
exports.batchRouter.post('/', async (req, res) => {
    const connection = await (0, db_js_1.getTransaction)();
    try {
        const { department, voucherIds, fromAudit = false } = req.body;
        // Validate required fields
        if (!department || !voucherIds || !Array.isArray(voucherIds) || voucherIds.length === 0) {
            return res.status(400).json({ error: 'Department and voucher IDs are required' });
        }
        // Check if user has access to create batches for this department
        if (req.user.department !== 'AUDIT' && req.user.department !== 'SYSTEM ADMIN' && department !== req.user.department) {
            return res.status(403).json({ error: 'Access denied' });
        }
        // ARCHITECTURAL FIX: Enhanced voucher validation with detailed error reporting
        for (const voucherId of voucherIds) {
            // Validate voucher ID format (should be UUID)
            const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
            if (!uuidRegex.test(voucherId)) {
                await connection.rollback();
                return res.status(400).json({
                    error: `Invalid voucher ID format: ${voucherId}. Expected UUID format.`,
                    details: 'Voucher IDs must be valid UUIDs. If you see timestamp-based IDs (e.g., v1234567890), this indicates a system configuration issue.'
                });
            }
            const vouchers = await connection.query('SELECT * FROM vouchers WHERE id = ? AND deleted = FALSE', [voucherId]);
            if (vouchers[0].length === 0) {
                await connection.rollback();
                return res.status(404).json({
                    error: `Voucher with ID ${voucherId} not found`,
                    details: 'The voucher may have been deleted or the ID is incorrect. Please refresh the page and try again.'
                });
            }
            const voucher = vouchers[0][0];
            if (voucher.department !== department && !fromAudit) {
                await connection.rollback();
                return res.status(400).json({ error: `Voucher with ID ${voucherId} does not belong to department ${department}` });
            }
        }
        // ENHANCED WORKFLOW: Calculate batch metadata including resubmissions
        let normalVoucherCount = 0;
        let rejectedVoucherCount = 0;
        let resubmissionCount = 0;
        for (const voucherId of voucherIds) {
            const [voucherCheck] = await connection.query('SELECT status, rejected_by, rejection_time FROM vouchers WHERE id = ?', [voucherId]);
            if (voucherCheck[0]) {
                const voucher = voucherCheck[0];
                // Check if this is a resubmission
                const isResubmission = (voucher.rejected_by && voucher.rejected_by.trim() !== '') ||
                    (voucher.rejection_time) ||
                    (voucher.status === 'VOUCHER REJECTED') ||
                    (voucher.status === 'VOUCHER RETURNED');
                if (voucher.status === 'VOUCHER REJECTED') {
                    rejectedVoucherCount++;
                }
                else if (isResubmission) {
                    resubmissionCount++;
                }
                else {
                    normalVoucherCount++;
                }
            }
        }
        const containsRejectedVouchers = rejectedVoucherCount > 0;
        const containsResubmissions = resubmissionCount > 0;
        console.log(`📦 ENHANCED BATCH COMPOSITION: ${normalVoucherCount} normal + ${rejectedVoucherCount} rejected + ${resubmissionCount} resubmissions`);
        // Create batch with enhanced metadata including resubmissions
        const batchId = (0, uuid_1.v4)();
        await connection.query(`INSERT INTO voucher_batches (
        id, department, sent_by, sent_time, received, from_audit,
        contains_rejected_vouchers, rejected_voucher_count,
        contains_resubmissions, resubmission_count
      ) VALUES (?, ?, ?, NOW(), ?, ?, ?, ?, ?, ?)`, [
            batchId, department, req.user.name, false, fromAudit,
            containsRejectedVouchers, rejectedVoucherCount,
            containsResubmissions, resubmissionCount
        ]);
        logger_js_1.logger.info(`📦 Batch created: ${batchId} - Contains ${rejectedVoucherCount} rejected + ${resubmissionCount} resubmission voucher(s)`);
        // Add vouchers to batch
        for (const voucherId of voucherIds) {
            await connection.query('INSERT INTO batch_vouchers (batch_id, voucher_id) VALUES (?, ?)', [batchId, voucherId]);
            // SIMPLE WORKFLOW: Get voucher details
            const currentVoucherQuery = await connection.query('SELECT status FROM vouchers WHERE id = ?', [voucherId]);
            const currentVoucherRows = currentVoucherQuery[0];
            const currentVoucher = currentVoucherRows && currentVoucherRows.length > 0 ? currentVoucherRows[0] : null;
            if (!currentVoucher) {
                logger_js_1.logger.error(`❌ Voucher ${voucherId} not found during batch dispatch`);
                continue;
            }
            // SIMPLE WORKFLOW: Handle voucher dispatch
            let newStatus;
            let updateQuery;
            let updateParams;
            if (fromAudit && currentVoucher.status === legacy_voucher_status_compat_js_1.VOUCHER_STATUSES.VOUCHER_REJECTED) {
                // REJECTED VOUCHER: Original rejected voucher being dispatched back to Finance
                // This goes to Finance REJECTED tab (NOT DISPATCHED tab - only certified vouchers go there)
                newStatus = legacy_voucher_status_compat_js_1.VOUCHER_STATUSES.VOUCHER_REJECTED;
                const { flags } = (0, legacy_voucher_status_compat_js_1.synchronizeVoucherFlags)({ status: newStatus });
                updateQuery = `
          UPDATE vouchers SET
            batch_id = ?,
            status = ?,
            flags = ?,
            audit_dispatch_time = NOW(),
            audit_dispatched_by = ?,
            department = ?,
            dispatched = 0
          WHERE id = ?
        `;
                updateParams = [batchId, newStatus, JSON.stringify(flags), req.user.name, department, voucherId];
                logger_js_1.logger.info(`📤 REJECTED VOUCHER: Dispatching ${voucherId} FROM AUDIT TO ${department} REJECTED tab - Status: ${newStatus}`);
            }
            else if (fromAudit) {
                // NORMAL VOUCHER: Being dispatched FROM Audit TO Department
                newStatus = legacy_voucher_status_compat_js_1.VOUCHER_STATUSES.VOUCHER_CERTIFIED;
                const { flags } = (0, legacy_voucher_status_compat_js_1.synchronizeVoucherFlags)({ status: newStatus });
                updateQuery = 'UPDATE vouchers SET batch_id = ?, status = ?, flags = ?, audit_dispatch_time = NOW(), audit_dispatched_by = ?, original_department = ?, department = ? WHERE id = ?';
                updateParams = [batchId, newStatus, JSON.stringify(flags), req.user.name, department, department, voucherId];
                logger_js_1.logger.info(`📤 NORMAL: Dispatching ${voucherId} FROM AUDIT TO ${department} - Status: ${newStatus}`);
            }
            else {
                // VOUCHER GOING TO AUDIT: Check for resubmission
                // RESUBMISSION FIX: Get full voucher details for resubmission detection
                const [fullVoucherQuery] = await connection.query('SELECT * FROM vouchers WHERE id = ?', [voucherId]);
                const voucher = fullVoucherQuery[0];
                // RESUBMISSION FIX: Detect if this is a re-submitted voucher
                const condition1 = voucher.rejected_by && voucher.rejected_by.trim() !== '';
                const condition2 = voucher.rejection_time;
                const condition3 = voucher.status === 'VOUCHER REJECTED';
                const condition4 = voucher.status === 'VOUCHER RETURNED';
                logger_js_1.logger.info(`🔍 RESUBMISSION DEBUG - Voucher ${voucher.voucher_id}:`, {
                    voucherId: voucher.voucher_id,
                    status: voucher.status,
                    rejected_by: voucher.rejected_by || 'NULL',
                    rejection_time: voucher.rejection_time || 'NULL',
                    comment: voucher.comment || 'NULL'
                });
                logger_js_1.logger.info(`🧪 RESUBMISSION CONDITIONS - ${voucher.voucher_id}:`, {
                    condition1_rejected_by: condition1,
                    condition2_rejection_time: condition2,
                    condition3_status_rejected: condition3,
                    condition4_status_returned: condition4
                });
                const isResubmission = condition1 || condition2 || condition3 || condition4;
                logger_js_1.logger.info(`🎯 RESUBMISSION RESULT - ${voucher.voucher_id}: ${isResubmission ? 'YES' : 'NO'}`);
                if (isResubmission) {
                    // RESUBMISSION: Set special status and workflow state
                    newStatus = 'RE-SUBMISSION';
                    const workflowState = 'FINANCE_RESUBMISSION';
                    const { flags } = (0, legacy_voucher_status_compat_js_1.synchronizeVoucherFlags)({ status: newStatus });
                    updateQuery = `UPDATE vouchers SET
            batch_id = ?, status = ?, workflow_state = ?, flags = ?,
            sent_to_audit = TRUE, dispatch_to_audit_by = ?, dispatch_time = NOW(),
            is_resubmitted = TRUE, department = 'AUDIT'
            WHERE id = ?`;
                    updateParams = [batchId, newStatus, workflowState, JSON.stringify(flags), req.user.name, voucherId];
                    logger_js_1.logger.info(`🔄 RESUBMISSION: Sending ${voucherId} FROM ${department} TO AUDIT - Status: ${newStatus}, Workflow: ${workflowState}`);
                }
                else {
                    // NORMAL SUBMISSION: Standard logic
                    newStatus = legacy_voucher_status_compat_js_1.VOUCHER_STATUSES.PENDING_RECEIPT;
                    const { flags } = (0, legacy_voucher_status_compat_js_1.synchronizeVoucherFlags)({ status: newStatus });
                    updateQuery = 'UPDATE vouchers SET batch_id = ?, status = ?, flags = ?, sent_to_audit = TRUE, dispatch_to_audit_by = ?, dispatch_time = NOW() WHERE id = ?';
                    updateParams = [batchId, newStatus, JSON.stringify(flags), req.user.name, voucherId];
                    logger_js_1.logger.info(`📥 NORMAL: Sending ${voucherId} FROM ${department} TO AUDIT - Status: ${newStatus}`);
                }
            }
            // Execute the update
            await connection.query(updateQuery, updateParams);
        }
        // PRODUCTION-LEVEL FIX: Single notification creation mechanism
        // Eliminate duplicate notifications by using ONLY database insertion
        // The event bus emission was causing duplicate notifications
        // Determine target department and message based on direction
        const targetDepartment = fromAudit ? department : 'AUDIT';
        // STEP 4 FIX: rejectedVoucherCount already calculated above, no need to recalculate
        // Create more informative notification message
        let notificationMessage;
        if (fromAudit) {
            if (rejectedVoucherCount > 0) {
                const totalVouchers = voucherIds.length;
                const acceptedCount = totalVouchers - rejectedVoucherCount;
                if (rejectedVoucherCount === totalVouchers) {
                    notificationMessage = `Batch received from Audit: ${rejectedVoucherCount} rejected voucher${rejectedVoucherCount > 1 ? 's' : ''}`;
                }
                else {
                    notificationMessage = `Batch received from Audit: ${acceptedCount} voucher${acceptedCount > 1 ? 's' : ''}, ${rejectedVoucherCount} rejected`;
                }
            }
            else {
                notificationMessage = `New batch received from Audit`;
            }
        }
        else {
            notificationMessage = `New batch received from ${department}`;
        }
        // Create SINGLE notification via database insertion
        logger_js_1.logger.info(`📤 Creating single notification for ${targetDepartment} department`);
        const notificationId = (0, uuid_1.v4)();
        await connection.query(`INSERT INTO notifications (
        id, user_id, message, is_read, timestamp, batch_id, type, from_audit
      ) VALUES (?, ?, ?, ?, NOW(), ?, ?, ?)`, [
            notificationId,
            targetDepartment,
            notificationMessage,
            false,
            batchId,
            'NEW_BATCH',
            fromAudit
        ]);
        logger_js_1.logger.info(`✅ Created single database notification ${notificationId} for ${targetDepartment} department`);
        // SIMPLE WORKFLOW: Batch completed
        logger_js_1.logger.info(`✅ Batch ${batchId} completed with metadata: ${normalVoucherCount} normal, ${rejectedVoucherCount} rejected`);
        // Commit transaction
        await connection.commit();
        // Get created batch with vouchers
        const batches = await (0, db_js_1.query)('SELECT * FROM voucher_batches WHERE id = ?', [batchId]);
        const batchVouchers = await (0, db_js_1.query)(`SELECT v.* FROM vouchers v
       JOIN batch_vouchers bv ON v.id = bv.voucher_id
       WHERE bv.batch_id = ?`, [batchId]);
        const result = batches[0];
        result.vouchers = batchVouchers;
        result.voucherIds = batchVouchers.map((v) => v.id);
        // PRODUCTION-LEVEL FIX: Use ONLY batch creation event, NO notification event
        // Emit batch creation event for real-time updates
        simpleEventBus_js_1.simpleEventBus.emitBatchCreated(result);
        logger_js_1.logger.info(`📡 Emitted batch creation event for ${batchId}`);
        // REMOVED: Duplicate notification creation via event bus
        // This was causing the second notification to appear
        // The database notification above is sufficient
        logger_js_1.logger.info(`✅ Successfully processed batch ${batchId} with ${batchVouchers.length} vouchers`);
        res.status(201).json(result);
    }
    catch (error) {
        await connection.rollback();
        logger_js_1.logger.error('Create batch error:', error);
        res.status(500).json({ error: 'Failed to create batch' });
    }
    finally {
        connection.release();
    }
});
// Receive batch
exports.batchRouter.post('/:id/receive', async (req, res) => {
    const connection = await (0, db_js_1.getTransaction)();
    try {
        const batchId = req.params.id;
        const { receivedVoucherIds = [], rejectedVoucherIds = [], rejectionComments = {} } = req.body;
        // Get batch
        const batches = await connection.query('SELECT * FROM voucher_batches WHERE id = ?', [batchId]);
        if (batches[0].length === 0) {
            await connection.rollback();
            return res.status(404).json({ error: 'Batch not found' });
        }
        const receiveBatch = batches[0][0];
        // Check if user has access to receive this batch
        const isFromAudit = receiveBatch.from_audit;
        if (isFromAudit) {
            // If batch is from Audit, only the department can receive it
            if (req.user.department !== receiveBatch.department && req.user.department !== 'SYSTEM ADMIN') {
                await connection.rollback();
                return res.status(403).json({ error: 'Access denied' });
            }
        }
        else {
            // If batch is from department to Audit, only Audit can receive it
            if (req.user.department !== 'AUDIT' && req.user.department !== 'SYSTEM ADMIN') {
                await connection.rollback();
                return res.status(403).json({ error: 'Access denied' });
            }
        }
        // Mark batch as received
        await connection.query('UPDATE voucher_batches SET received = TRUE WHERE id = ?', [batchId]);
        // Get all vouchers in this batch
        const batchVouchers = await connection.query(`SELECT v.* FROM vouchers v
       JOIN batch_vouchers bv ON v.id = bv.voucher_id
       WHERE bv.batch_id = ?`, [batchId]);
        const allVoucherIds = batchVouchers[0].map((v) => v.id);
        // Process received vouchers
        for (const voucherId of receivedVoucherIds) {
            if (!allVoucherIds.includes(voucherId)) {
                continue; // Skip if voucher is not in this batch
            }
            // Get current voucher state
            const [currentVoucher] = await connection.query('SELECT * FROM vouchers WHERE id = ? FOR UPDATE', [voucherId]);
            if (!currentVoucher[0]) {
                continue;
            }
            const voucher = currentVoucher[0];
            // WORKFLOW FIX: Get the appropriate status based on direction and workflow
            let newStatus;
            if (isFromAudit) {
                // Batch from Audit to Department - vouchers are being RECEIVED by department
                // REJECTION WORKFLOW FIX: Check for rejection copies first
                if (voucher.is_rejection_copy && voucher.rejected_by && voucher.rejection_time) {
                    newStatus = legacy_voucher_status_compat_js_1.VOUCHER_STATUSES.VOUCHER_REJECTED; // Rejection copies go to REJECTED tab
                    logger_js_1.logger.info(`🔄 REJECTION COPY: Setting REJECTED status for copy voucher ${voucherId} (rejected by ${voucher.rejected_by})`);
                }
                else if (voucher.rejected_by && voucher.rejection_time && !voucher.is_rejection_copy) {
                    newStatus = legacy_voucher_status_compat_js_1.VOUCHER_STATUSES.VOUCHER_REJECTED; // Permanent rejected records
                    logger_js_1.logger.info(`🔄 PERMANENT REJECTED: Restoring REJECTED status for voucher ${voucherId} (rejected by ${voucher.rejected_by})`);
                }
                else if (voucher.pending_return || voucher.is_returned) {
                    newStatus = legacy_voucher_status_compat_js_1.VOUCHER_STATUSES.VOUCHER_PROCESSING; // Returned vouchers go back to processing
                }
                else {
                    // CRITICAL FIX: Normal vouchers become CERTIFIED when department receives them
                    newStatus = legacy_voucher_status_compat_js_1.VOUCHER_STATUSES.VOUCHER_CERTIFIED;
                }
            }
            else {
                // Batch from Department to Audit - vouchers are being ACCEPTED by audit
                // When audit receives vouchers, they should go to AUDIT: PROCESSING status
                newStatus = legacy_voucher_status_compat_js_1.VOUCHER_STATUSES.AUDIT_PROCESSING;
            }
            const { flags } = (0, legacy_voucher_status_compat_js_1.synchronizeVoucherFlags)({ status: newStatus });
            // Validate the status transition
            // CRITICAL FIX: Safe JSON parsing for voucher flags
            let currentFlags = {};
            if (voucher.flags) {
                try {
                    // If flags is already an object, use it directly
                    if (typeof voucher.flags === 'object') {
                        currentFlags = voucher.flags;
                    }
                    else {
                        // If flags is a string, try to parse it
                        currentFlags = JSON.parse(voucher.flags);
                    }
                }
                catch (error) {
                    logger_js_1.logger.warn(`Invalid flags JSON for voucher ${voucherId}: ${voucher.flags}. Using empty flags.`);
                    currentFlags = {};
                }
            }
            // CRITICAL DEBUG: Log validation parameters
            logger_js_1.logger.info(`🔍 VALIDATION DEBUG: Checking transition for voucher ${voucherId}:`, {
                currentStatus: voucher.status,
                newStatus: newStatus,
                userRole: req.user.role,
                userDepartment: req.user.department,
                userName: req.user.name,
                currentFlags: currentFlags
            });
            // CRITICAL FIX: Skip status transition validation for batch receive operations
            // Batch receive is a system-level operation that should always be allowed
            logger_js_1.logger.info(`🔄 BATCH RECEIVE: Bypassing status validation for system operation ${voucher.status} -> ${newStatus}`, {
                userRole: req.user.role,
                userDepartment: req.user.department,
                userName: req.user.name,
                voucherId: voucher.id
            });
            // Log the status change
            logger_js_1.logger.info(`🔄 AUDIT ACCEPTANCE: Changing voucher ${voucherId} status: ${voucher.status} → ${newStatus} (received by ${req.user.name})`);
            logger_js_1.logger.info(`🔄 AUDIT ACCEPTANCE: Setting received_by_audit = ${!isFromAudit} for voucher ${voucherId}`);
            // ARCHITECTURAL FIX: Update voucher with proper audit processing fields (using existing schema)
            if (isFromAudit) {
                // Vouchers being received by department from audit - restore to original department
                // CRITICAL FIX: Implement voucher offset logic
                // When a processed voucher returns from audit, it should offset (replace) the original voucher in Processing tab
                const currentVoucherData = currentVoucher[0];
                if (currentVoucherData.reference_id) {
                    // This processed voucher has a reference_id, meaning it should offset an original voucher
                    logger_js_1.logger.info(`🔄 OFFSET LOGIC: Processing voucher ${voucherId} with reference_id ${currentVoucherData.reference_id}`);
                    // Find and remove the original voucher from Processing tab
                    const [originalVouchers] = await connection.query(`SELECT id, status FROM vouchers
             WHERE voucher_id = ? AND department = ? AND status IN ('PENDING', 'VOUCHER PROCESSING')`, [currentVoucherData.reference_id, currentVoucherData.original_department || currentVoucherData.department]);
                    if (originalVouchers.length > 0) {
                        const originalVoucher = originalVouchers[0];
                        logger_js_1.logger.info(`🔄 OFFSET LOGIC: Found original voucher ${originalVoucher.id} with status ${originalVoucher.status} - removing from Processing tab`);
                        // Remove the original voucher (offset logic)
                        await connection.query(`UPDATE vouchers SET
               status = 'OFFSET_BY_AUDIT',
               deleted = TRUE,
               deletion_time = NOW()
               WHERE id = ?`, [originalVoucher.id]);
                        logger_js_1.logger.info(`✅ OFFSET LOGIC: Original voucher ${originalVoucher.id} offset by processed voucher ${voucherId}`);
                    }
                    else {
                        logger_js_1.logger.warn(`⚠️ OFFSET LOGIC: No original voucher found for reference_id ${currentVoucherData.reference_id}`);
                    }
                }
                // Update the processed voucher to appear in appropriate tab
                // CRITICAL FIX: For rejected vouchers, ensure department is set to receiving department
                const targetDepartment = newStatus === legacy_voucher_status_compat_js_1.VOUCHER_STATUSES.VOUCHER_REJECTED
                    ? req.user.department // For rejected vouchers, set to receiving department
                    : (voucher.original_department || voucher.department); // For others, restore original
                // RESUBMISSION FIX: Preserve resubmission status and workflow state when receiving
                const isResubmittedVoucher = voucher.status === 'RE-SUBMISSION' ||
                    voucher.is_resubmitted ||
                    (voucher.rejected_by && voucher.rejected_by.trim() !== '') ||
                    voucher.rejection_time;
                // CERTIFIED RESUBMISSION FIX: If resubmission is certified, use certified status
                const isCertifiedResubmission = isResubmittedVoucher && voucher.certified_by;
                const finalStatus = isCertifiedResubmission ? legacy_voucher_status_compat_js_1.VOUCHER_STATUSES.VOUCHER_CERTIFIED :
                    (isResubmittedVoucher ? 'RE-SUBMISSION' : newStatus);
                const finalWorkflowState = isResubmittedVoucher ? 'FINANCE_RESUBMISSION_RECEIVED' : 'FINANCE_PROCESSING';
                logger_js_1.logger.info(`🔄 FINANCE RECEIVING: Voucher ${voucherId} - isResubmitted: ${isResubmittedVoucher}, finalStatus: ${finalStatus}, workflow_state: ${finalWorkflowState}`);
                await connection.query(`UPDATE vouchers SET
           status = ?,
           workflow_state = ?,
           flags = ?,
           department = ?,
           received_by = ?,
           receipt_time = NOW(),
           department_receipt_time = NOW(),
           department_received_by = ?
           WHERE id = ?`, [finalStatus, finalWorkflowState, JSON.stringify(flags), targetDepartment, req.user.name, req.user.name, voucherId]);
            }
            else {
                // CRITICAL FIX: Vouchers being RECEIVED by audit from department - transfer to AUDIT department
                // PRESERVE original department for proper tab filtering
                // RESUBMISSION FIX: Check if this is a resubmitted voucher to set correct workflow state
                // Check multiple conditions to detect resubmissions
                const isResubmittedVoucher = voucher.status === 'RE-SUBMISSION' ||
                    voucher.is_resubmitted ||
                    (voucher.rejected_by && voucher.rejected_by.trim() !== '') ||
                    voucher.rejection_time;
                const auditWorkflowState = isResubmittedVoucher ? 'AUDIT_NEW_RESUBMITTED' : 'AUDIT_NEW';
                logger_js_1.logger.info(`🔄 AUDIT RECEIVING: Voucher ${voucherId} - isResubmitted: ${isResubmittedVoucher}, workflow_state: ${auditWorkflowState}`);
                await connection.query(`UPDATE vouchers SET
           status = ?,
           flags = ?,
           department = 'AUDIT',
           original_department = COALESCE(original_department, department),
           received_by = ?,
           receipt_time = NOW(),
           received_by_audit = TRUE,
           pre_audited_amount = NULL,
           pre_audited_by = NULL,
           certified_by = NULL,
           work_started = FALSE,
           comment = NULL,
           workflow_state = ?
           WHERE id = ?`, [newStatus, JSON.stringify(flags), req.user.name, auditWorkflowState, voucherId]);
            }
            logger_js_1.logger.info(`✅ AUDIT PROCESSING: Successfully updated voucher ${voucherId} with status ${newStatus}`);
            // ARCHITECTURAL FIX: Create appropriate notifications based on processing type
            if (!isFromAudit) {
                // Vouchers accepted/certified by audit - notify department
                const notificationId = (0, uuid_1.v4)();
                await connection.query(`INSERT INTO notifications (
            id, user_id, message, is_read, timestamp, voucher_id, type
          ) VALUES (?, ?, ?, ?, NOW(), ?, ?)`, [
                    notificationId,
                    voucher.department,
                    `Voucher ${voucher.voucher_id} certified by Audit`,
                    false,
                    voucherId,
                    'VOUCHER_CERTIFIED'
                ]);
            }
            else {
                // Vouchers received by department from audit - notify department
                const notificationId = (0, uuid_1.v4)();
                await connection.query(`INSERT INTO notifications (
            id, user_id, message, is_read, timestamp, voucher_id, type
          ) VALUES (?, ?, ?, ?, NOW(), ?, ?)`, [
                    notificationId,
                    voucher.department,
                    `Voucher ${voucher.voucher_id} received from Audit`,
                    false,
                    voucherId,
                    'VOUCHER_RECEIVED'
                ]);
            }
        }
        // Process rejected vouchers
        for (const voucherId of rejectedVoucherIds) {
            if (!allVoucherIds.includes(voucherId)) {
                continue; // Skip if voucher is not in this batch
            }
            const comment = rejectionComments[voucherId] || '';
            // SIMPLE REJECTION WORKFLOW: Just reject the voucher, no dual-tab complexity
            console.log(`🚫 BATCH REJECTION: Rejecting voucher ${voucherId} with simple workflow`);
            // Get the voucher details
            const [voucherDetails] = await connection.query('SELECT * FROM vouchers WHERE id = ?', [voucherId]);
            if (voucherDetails.length === 0) {
                logger_js_1.logger.error(`Voucher ${voucherId} not found during batch rejection`);
                continue;
            }
            const voucherToReject = voucherDetails[0];
            // SIMPLE WORKFLOW: Just reject the original voucher (goes to PENDING DISPATCH for dispatch back to Finance)
            const { flags: rejectedFlags } = (0, legacy_voucher_status_compat_js_1.synchronizeVoucherFlags)({ status: legacy_voucher_status_compat_js_1.VOUCHER_STATUSES.VOUCHER_REJECTED });
            await connection.query(`
        UPDATE vouchers SET
          status = ?,
          flags = ?,
          rejected_by = ?,
          rejection_time = NOW(),
          comment = ?,
          work_started = 1,
          department = 'AUDIT',
          rejection_type = 'DISPATCHABLE',
          workflow_state = 'AUDIT_PENDING_DISPATCH_REJECTED'
        WHERE id = ?
      `, [
                legacy_voucher_status_compat_js_1.VOUCHER_STATUSES.VOUCHER_REJECTED,
                JSON.stringify(rejectedFlags),
                req.user.name,
                comment,
                voucherId
            ]);
            // STEP 2: Create COPY for Audit REJECTED tab (permanent record)
            console.log(`Creating copy for Audit REJECTED tab (permanent record)`);
            const copyId = (0, uuid_1.v4)();
            const { flags: copyFlags } = (0, legacy_voucher_status_compat_js_1.synchronizeVoucherFlags)({
                status: legacy_voucher_status_compat_js_1.VOUCHER_STATUSES.VOUCHER_REJECTED
            });
            await connection.query(`
        INSERT INTO vouchers (
          id, voucher_id, date, claimant, description, amount, currency,
          department, original_department, status, flags,
          created_by, created_at, sent_to_audit, received_by_audit, work_started,
          parent_voucher_id, is_rejection_copy,
          rejected_by, rejection_time, comment, rejection_type, workflow_state
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
                copyId,
                voucherToReject.voucher_id + '-COPY', // Copy for Finance Voucher Hub
                voucherToReject.date,
                voucherToReject.claimant,
                voucherToReject.description,
                voucherToReject.amount,
                voucherToReject.currency,
                'AUDIT', // Copy stays in Audit department for REJECTED tab
                voucherToReject.original_department,
                legacy_voucher_status_compat_js_1.VOUCHER_STATUSES.VOUCHER_REJECTED,
                JSON.stringify(copyFlags),
                voucherToReject.created_by,
                voucherToReject.created_at,
                true, // sent_to_audit
                true, // received_by_audit
                false, // work_started (copy is permanent record, not for dispatch)
                voucherId, // Link to original voucher
                true, // is_rejection_copy
                req.user.name,
                new Date().toISOString().slice(0, 19).replace('T', ' '),
                comment,
                'PERMANENT_RECORD', // Copy is permanent record for Audit REJECTED tab
                'AUDIT_REJECTED_COPY' // Workflow state for copy in Audit REJECTED tab
            ]);
            console.log(`✅ BATCH REJECTION: Simple rejection completed for ${voucherToReject.voucher_id}:`);
            console.log(`   - Original voucher: ${voucherId} (goes to Audit PENDING DISPATCH tab)`);
            console.log(`   - Copy: ${copyId} (goes to Audit REJECTED tab as permanent record)`);
            // Create notification for department
            const voucher = batchVouchers[0].find((v) => v.id === voucherId);
            if (voucher) {
                const notificationId = (0, uuid_1.v4)();
                await connection.query(`INSERT INTO notifications (
            id, user_id, message, is_read, timestamp, voucher_id, type, from_audit
          ) VALUES (?, ?, ?, ?, NOW(), ?, ?, ?)`, [
                    notificationId,
                    isFromAudit ? 'AUDIT' : voucher.department,
                    `Voucher ${voucher.voucher_id} rejected`,
                    false,
                    voucherId,
                    'VOUCHER_REJECTED',
                    !isFromAudit
                ]);
            }
        }
        // ARCHITECTURAL FIX: Mark batch as received and create return batch for certified vouchers ONLY
        await connection.query('UPDATE voucher_batches SET received = TRUE WHERE id = ?', [batchId]);
        // CRITICAL FIX: Only create return batch for ACCEPTED vouchers, NOT rejected ones
        // Rejected vouchers should stay in AUDIT for manual dispatch through PENDING DISPATCH tab
        const returnBatch = batches[0][0];
        const allProcessedVoucherIds = [...receivedVoucherIds]; // REMOVED rejectedVoucherIds
        // PRODUCTION-LEVEL FIX: Create ONE return batch with duplicate prevention
        if (!isFromAudit && allProcessedVoucherIds.length > 0) {
            // Check for existing return batches with same vouchers to prevent duplicates
            const existingReturnBatches = await connection.query(`SELECT DISTINCT vb.id FROM voucher_batches vb
         JOIN batch_vouchers bv ON vb.id = bv.batch_id
         WHERE vb.department = ? AND vb.from_audit = TRUE
         AND vb.sent_time > DATE_SUB(NOW(), INTERVAL 2 MINUTE)
         AND bv.voucher_id IN (${allProcessedVoucherIds.map(() => '?').join(',')})`, [returnBatch.department, ...allProcessedVoucherIds]);
            if (existingReturnBatches.length > 0) {
                logger_js_1.logger.warn(`⚠️ Duplicate return batch prevented - existing batch found for department ${returnBatch.department}`);
            }
            else {
                const returnBatchId = (0, uuid_1.v4)();
                await connection.query(`INSERT INTO voucher_batches (
            id, department, sent_by, sent_time, received, from_audit
          ) VALUES (?, ?, ?, NOW(), FALSE, TRUE)`, [
                    returnBatchId,
                    returnBatch.department,
                    req.user.name
                ]);
                // Add ALL processed vouchers to the single return batch
                for (const voucherId of allProcessedVoucherIds) {
                    await connection.query('INSERT INTO batch_vouchers (batch_id, voucher_id) VALUES (?, ?)', [returnBatchId, voucherId]);
                }
                logger_js_1.logger.info(`✅ Created return batch ${returnBatchId} for ${allProcessedVoucherIds.length} processed vouchers (${receivedVoucherIds.length} accepted, ${rejectedVoucherIds.length} rejected) to ${returnBatch.department}`);
            }
        }
        // Commit transaction
        await connection.commit();
        // Get updated batch with vouchers
        const updatedBatches = await (0, db_js_1.query)('SELECT * FROM voucher_batches WHERE id = ?', [batchId]);
        const updatedBatchVouchers = await (0, db_js_1.query)(`SELECT v.* FROM vouchers v
       JOIN batch_vouchers bv ON v.id = bv.voucher_id
       WHERE bv.batch_id = ?`, [batchId]);
        const result = updatedBatches[0];
        result.vouchers = updatedBatchVouchers;
        result.voucherIds = updatedBatchVouchers.map((v) => v.id);
        res.status(200).json(result);
    }
    catch (error) {
        await connection.rollback();
        logger_js_1.logger.error('Receive batch error:', error);
        res.status(500).json({ error: 'Failed to receive batch' });
    }
    finally {
        connection.release();
    }
});
//# sourceMappingURL=batches.js.map