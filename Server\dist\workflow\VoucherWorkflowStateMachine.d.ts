/**
 * VMS Voucher Workflow State Machine
 * Single Source of Truth for all voucher workflow states and transitions
 * Based on END TO END VOUCHER WORKFLOW.txt specification
 */
export declare enum WorkflowState {
    FINANCE_PENDING = "FINANCE_PENDING",
    FINANCE_PROCESSING = "FINANCE_PROCESSING",
    FINANCE_CERTIFIED = "FINANCE_CERTIFIED",
    FINANCE_REJECTED = "FINANCE_REJECTED",
    FINANCE_RETURNED = "FINANCE_RETURNED",
    FINANCE_RESUBMISSION_RECEIVED = "FINANCE_RESUBMISSION_RECEIVED",
    AUDIT_NEW = "AUDIT_NEW",
    AUDIT_NEW_RESUBMITTED = "AUDIT_NEW_RESUBMITTED",
    AUDIT_PENDING_DISPATCH = "AUDIT_PENDING_DISPATCH",
    AUDIT_PENDING_DISPATCH_REJECTED = "AUDIT_PENDING_DISPATCH_REJECTED",
    AUDIT_PENDING_DISPATCH_RETURNED = "AUDIT_PENDING_DISPATCH_RETURNED",
    AUDIT_DISPATCHED = "AUDIT_DISPATCHED",
    AUDIT_REJECTED_COPY = "AUDIT_REJECTED_COPY",
    AUDIT_RETURNED_COPY = "AUDIT_RETURNED_COPY"
}
export declare enum WorkflowEvent {
    CREATE_VOUCHER = "CREATE_VOUCHER",
    SUBMIT_TO_AUDIT = "SUBMIT_TO_AUDIT",
    RESUBMIT_FROM_REJECTED = "RESUBMIT_FROM_REJECTED",
    RESUBMIT_FROM_RETURNED = "RESUBMIT_FROM_RETURNED",
    RECEIVE_FROM_FINANCE = "RECEIVE_FROM_FINANCE",
    START_WORK = "START_WORK",
    CERTIFY_VOUCHER = "CERTIFY_VOUCHER",
    REJECT_VOUCHER = "REJECT_VOUCHER",
    RETURN_VOUCHER = "RETURN_VOUCHER",
    DISPATCH_TO_FINANCE = "DISPATCH_TO_FINANCE"
}
export declare enum BadgeType {
    NONE = "NONE",
    RE_SUBMITTED = "RE_SUBMITTED",
    RESUBMISSION = "RESUBMISSION",
    RETURNED = "RETURNED",
    REJECTED = "REJECTED"
}
export interface WorkflowTransition {
    fromState: WorkflowState;
    event: WorkflowEvent;
    toState: WorkflowState;
    isValid: boolean;
    requiresCopy?: boolean;
    copyState?: WorkflowState;
    badge?: BadgeType;
}
export interface VoucherWorkflowContext {
    voucherId: string;
    userId: string;
    userDepartment: string;
    originalDepartment: string;
    timestamp: Date;
    metadata?: Record<string, any>;
}
export declare class VoucherWorkflowStateMachine {
    private static readonly TRANSITIONS;
    private static readonly STATE_TO_TAB_MAPPING;
    /**
     * Get valid transition for given state and event
     */
    static getTransition(fromState: WorkflowState, event: WorkflowEvent): WorkflowTransition | null;
    /**
     * Get tab name for voucher based on workflow state and user department
     */
    static getTabForVoucher(workflowState: WorkflowState, userDepartment: string): string | null;
    /**
     * Validate if transition is allowed
     */
    static canTransition(fromState: WorkflowState, event: WorkflowEvent): boolean;
    /**
     * Get all possible events for a given state
     */
    static getValidEvents(fromState: WorkflowState): WorkflowEvent[];
    /**
     * Get badge type for workflow state
     */
    static getBadgeType(workflowState: WorkflowState, context?: VoucherWorkflowContext): BadgeType;
    /**
     * Get all available tabs for department
     */
    static getAvailableTabs(userDepartment: string): string[];
    /**
     * Check if voucher is editable based on workflow state
     */
    static isVoucherEditable(voucher: any, userDepartment: string): boolean;
}
