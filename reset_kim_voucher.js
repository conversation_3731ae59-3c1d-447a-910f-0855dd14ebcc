const mysql = require('mysql2/promise');
require('dotenv').config();

async function resetKimVoucher() {
  let connection;
  
  try {
    // Create connection
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || 'vms@2025@1989',
      database: process.env.DB_NAME || 'vms_production'
    });

    console.log('🔗 Connected to database');
    console.log('🔄 Resetting KIM voucher back to PENDING SUBMISSION for resubmission test...\n');

    // Reset the KIM voucher back to PENDING SUBMISSION (as if "Add Back" was just clicked)
    await connection.execute(`
      UPDATE vouchers 
      SET status = 'PENDING SUBMISSION',
          workflow_state = 'FINANCE_PENDING',
          sent_to_audit = false,
          is_resubmission = false
      WHERE voucher_id = 'FINJUL0004' AND claimant = 'KIM'
    `);

    console.log('✅ KIM voucher reset to PENDING SUBMISSION');
    console.log('   - Status: PENDING SUBMISSION');
    console.log('   - Workflow State: FINANCE_PENDING');
    console.log('   - Sent to Audit: false');
    console.log('   - Is Resubmission: false');
    console.log('   - Rejection info preserved (rejected_by, rejection_time, comment)');

    // Verify the reset
    const [results] = await connection.execute(`
      SELECT voucher_id, claimant, status, workflow_state, is_resubmission, sent_to_audit, rejected_by
      FROM vouchers 
      WHERE voucher_id = 'FINJUL0004' AND claimant = 'KIM'
    `);

    if (results.length > 0) {
      const voucher = results[0];
      console.log('\n📊 Verified voucher state:');
      console.log(`   Voucher ID: ${voucher.voucher_id}`);
      console.log(`   Claimant: ${voucher.claimant}`);
      console.log(`   Status: ${voucher.status}`);
      console.log(`   Workflow State: ${voucher.workflow_state}`);
      console.log(`   Is Resubmission: ${voucher.is_resubmission ? 'YES' : 'NO'}`);
      console.log(`   Sent to Audit: ${voucher.sent_to_audit ? 'YES' : 'NO'}`);
      console.log(`   Rejected By: ${voucher.rejected_by || 'None'}`);
    }

    console.log('\n🎯 NOW TEST THE FIX:');
    console.log('1. Go to Finance Dashboard → PENDING tab');
    console.log('2. Select KIM voucher (FINJUL0004)');
    console.log('3. Choose dispatcher and click "Send to Audit"');
    console.log('4. Status should change to "RE-SUBMISSION"');
    console.log('5. is_resubmission flag should be set to true');

  } catch (error) {
    console.error('❌ Error resetting KIM voucher:', error);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n🔌 Database connection closed');
    }
  }
}

// Run the reset
resetKimVoucher();
