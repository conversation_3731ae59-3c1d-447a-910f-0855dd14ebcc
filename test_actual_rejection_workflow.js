const mysql = require('mysql2/promise');

async function testRejectionWorkflow() {
  console.log('🔍 TESTING ACTUAL REJECTION WORKFLOW');
  console.log('====================================');
  
  const connection = await mysql.createConnection({
    host: 'localhost',
    user: 'root',
    password: 'vms@2025@1989',
    database: 'vms_production'
  });
  
  try {
    // Create a test voucher
    console.log('\n1️⃣ Creating test voucher...');
    const testVoucherId = 'REJTEST' + Date.now();
    const testId = Date.now();

    await connection.execute(`
      INSERT INTO vouchers (
        id, voucher_id, date, claimant, description, amount, currency,
        department, original_department, status, workflow_state,
        created_by, created_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
    `, [
      testId, testVoucherId, '2025-01-21', 'Test Claimant', 'Test rejection workflow',
      1000.00, 'GHS', 'FINANCE', 'FINANCE', 'PENDING', 'FINANCE_PENDING',
      'TEST USER'
    ]);
    
    console.log(`✅ Created voucher: ${testVoucherId}`);
    
    // Step 1: Finance sends to Audit
    console.log('\n2️⃣ Finance sends to Audit...');
    await connection.execute(`
      UPDATE vouchers SET 
        status = 'PENDING RECEIPT',
        workflow_state = 'FINANCE_PROCESSING',
        sent_to_audit = 1,
        department = 'FINANCE'
      WHERE voucher_id = ?
    `, [testVoucherId]);
    
    console.log('✅ Voucher sent to Audit (Finance PROCESSING tab)');
    
    // Step 2: Audit receives voucher
    console.log('\n3️⃣ Audit receives voucher...');
    await connection.execute(`
      UPDATE vouchers SET 
        status = 'VOUCHER PROCESSING',
        workflow_state = 'AUDIT_NEW',
        department = 'AUDIT',
        received_by_audit = 1
      WHERE voucher_id = ?
    `, [testVoucherId]);
    
    console.log('✅ Voucher received by Audit (Audit NEW VOUCHERS tab)');
    
    // Step 3: Audit rejects voucher
    console.log('\n4️⃣ Audit rejects voucher...');
    await connection.execute(`
      UPDATE vouchers SET 
        status = 'VOUCHER REJECTED',
        workflow_state = 'AUDIT_PENDING_DISPATCH_REJECTED',
        rejected_by = 'AUDIT USER',
        rejection_time = NOW(),
        comment = 'Test rejection: Missing documents',
        work_started = 1
      WHERE voucher_id = ?
    `, [testVoucherId]);
    
    console.log('✅ Voucher rejected (Audit PENDING DISPATCH tab)');
    
    // Step 4: Audit dispatches rejected voucher to Finance
    console.log('\n5️⃣ Audit dispatches rejected voucher to Finance...');
    await connection.execute(`
      UPDATE vouchers SET 
        workflow_state = 'FINANCE_REJECTED',
        department = 'FINANCE',
        dispatched_by = 'AUDIT USER',
        dispatch_time = NOW()
      WHERE voucher_id = ?
    `, [testVoucherId]);
    
    console.log('✅ Rejected voucher dispatched to Finance');
    
    // Check final state
    const [finalState] = await connection.execute(`
      SELECT voucher_id, status, workflow_state, department, rejected_by, comment
      FROM vouchers WHERE voucher_id = ?
    `, [testVoucherId]);
    
    console.log('\n📊 FINAL STATE:');
    const voucher = finalState[0];
    console.log('   voucher_id:', voucher.voucher_id);
    console.log('   status:', voucher.status);
    console.log('   workflow_state:', voucher.workflow_state);
    console.log('   department:', voucher.department);
    console.log('   rejected_by:', voucher.rejected_by);
    console.log('   comment:', voucher.comment);
    
    // Test tab mapping
    console.log('\n🔍 TAB MAPPING TEST:');
    const shouldAppearInFinanceRejected = voucher.workflow_state === 'FINANCE_REJECTED' && 
                                         voucher.department === 'FINANCE' &&
                                         voucher.status === 'VOUCHER REJECTED';
    
    console.log('   Should appear in Finance REJECTED tab:', shouldAppearInFinanceRejected ? '✅ YES' : '❌ NO');
    
    if (shouldAppearInFinanceRejected) {
      console.log('\n✅ REJECTION WORKFLOW WORKING CORRECTLY!');
    } else {
      console.log('\n❌ REJECTION WORKFLOW HAS ISSUES!');
      console.log('   Expected: workflow_state=FINANCE_REJECTED, department=FINANCE, status=VOUCHER REJECTED');
      console.log(`   Actual: workflow_state=${voucher.workflow_state}, department=${voucher.department}, status=${voucher.status}`);
    }
    
    // Clean up test voucher
    await connection.execute('DELETE FROM vouchers WHERE voucher_id = ?', [testVoucherId]);
    console.log('\n🧹 Test voucher cleaned up');
    
  } catch (error) {
    console.error('❌ Error testing rejection workflow:', error);
  } finally {
    await connection.end();
  }
}

testRejectionWorkflow().catch(console.error);
