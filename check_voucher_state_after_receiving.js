const mysql = require('mysql2/promise');

async function checkVoucherState() {
  let connection;
  try {
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root', 
      password: 'vms@2025@1989',
      database: 'vms_production'
    });
    
    console.log('🔍 CHECKING VOUCHER STATE AFTER AUDIT RECEIVING');
    console.log('===============================================');
    
    // Get all vouchers
    const [vouchers] = await connection.execute(`
      SELECT 
        id, voucher_id, claimant, status, workflow_state, department, original_department,
        is_resubmitted, rejected_by, rejection_time, batch_id, sent_to_audit, work_started
      FROM vouchers 
      ORDER BY created_at DESC
      LIMIT 5
    `);
    
    if (vouchers.length === 0) {
      console.log('❌ No vouchers found');
      return;
    }
    
    console.log(`📄 Found ${vouchers.length} voucher(s):`);
    
    vouchers.forEach((voucher, index) => {
      console.log(`\n${index + 1}. VOUCHER: ${voucher.voucher_id}`);
      console.log(`   ID: ${voucher.id}`);
      console.log(`   Claimant: ${voucher.claimant}`);
      console.log(`   Status: ${voucher.status}`);
      console.log(`   Workflow State: ${voucher.workflow_state}`);
      console.log(`   Department: ${voucher.department}`);
      console.log(`   Original Department: ${voucher.original_department}`);
      console.log(`   Is Resubmitted: ${voucher.is_resubmitted ? 'YES' : 'NO'}`);
      console.log(`   Rejected By: ${voucher.rejected_by || 'None'}`);
      console.log(`   Rejection Time: ${voucher.rejection_time || 'None'}`);
      console.log(`   Batch ID: ${voucher.batch_id || 'None'}`);
      console.log(`   Sent to Audit: ${voucher.sent_to_audit ? 'YES' : 'NO'}`);
      console.log(`   Work Started: ${voucher.work_started ? 'YES' : 'NO'}`);
      
      // Check badge conditions
      console.log(`\n   🎯 BADGE ANALYSIS:`);
      console.log(`   Current badge logic checks:`);
      console.log(`   - voucher.isResubmitted: ${voucher.is_resubmitted ? 'YES' : 'NO'}`);
      console.log(`   - voucher.status === 'RE-SUBMISSION': ${voucher.status === 'RE-SUBMISSION' ? 'YES' : 'NO'}`);
      console.log(`   - workflow_state === 'AUDIT_NEW_RESUBMITTED': ${voucher.workflow_state === 'AUDIT_NEW_RESUBMITTED' ? 'YES' : 'NO'}`);
      
      const shouldShowBadge = voucher.is_resubmitted || 
                             voucher.status === 'RE-SUBMISSION' || 
                             voucher.workflow_state === 'AUDIT_NEW_RESUBMITTED';
      
      console.log(`   Should show RESUBMISSION badge: ${shouldShowBadge ? 'YES' : 'NO'}`);
      
      if (voucher.workflow_state === 'AUDIT_NEW_RESUBMITTED' && !shouldShowBadge) {
        console.log(`   ❌ BADGE ISSUE: Workflow state indicates resubmission but badge logic fails`);
      } else if (shouldShowBadge) {
        console.log(`   ✅ BADGE SHOULD APPEAR: Conditions met for resubmission badge`);
      }
    });
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

checkVoucherState();
