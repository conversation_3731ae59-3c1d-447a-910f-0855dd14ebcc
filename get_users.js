const mysql = require('mysql2/promise');

async function getUsers() {
  let connection;
  try {
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: 'vms@2025@1989',
      database: 'vms_production'
    });
    
    const [users] = await connection.execute('SELECT name, department, password FROM users');
    console.log('Available users:');
    users.forEach(user => {
      console.log(`- ${user.name} (${user.department}) - Password: ${user.password}`);
    });
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    if (connection) await connection.end();
  }
}

getUsers();
