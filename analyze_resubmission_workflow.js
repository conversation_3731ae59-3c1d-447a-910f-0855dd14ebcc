const mysql = require('mysql2/promise');

async function analyzeResubmissionWorkflow() {
  const connection = await mysql.createConnection({
    host: 'localhost',
    user: 'root',
    password: 'vms@2025@1989',
    database: 'vms_production'
  });
  
  console.log('🔍 ANALYZING ACTUAL RESUBMISSION WORKFLOW');
  console.log('=========================================');
  
  // Check FINJUL0001 current state
  const [vouchers] = await connection.execute(`
    SELECT 
      voucher_id, status, workflow_state, department, original_department,
      certified_by, is_resubmitted, dispatched_by, sent_to_audit,
      rejected_by, rejection_time, comment, created_at
    FROM vouchers 
    WHERE voucher_id = 'FINJUL0001'
  `);
  
  if (vouchers.length > 0) {
    const voucher = vouchers[0];
    console.log('\n📋 FINJUL0001 COMPLETE STATE:');
    Object.keys(voucher).forEach(key => {
      console.log(`   ${key}:`, voucher[key]);
    });
    
    console.log('\n🔍 WORKFLOW ANALYSIS:');
    console.log('====================');
    
    // Analyze the actual workflow states used in resubmission
    console.log('\n1️⃣ RESUBMISSION WORKFLOW STATES (from code analysis):');
    console.log('   - Finance sends resubmission: FINANCE_RESUBMISSION');
    console.log('   - Audit receives resubmission: AUDIT_NEW_RESUBMITTED');
    console.log('   - Audit certifies resubmission: AUDIT_DISPATCHED');
    console.log('   - Finance receives certified: FINANCE_RESUBMISSION_RECEIVED');
    
    console.log('\n2️⃣ CURRENT VOUCHER STATE:');
    console.log(`   - workflow_state: ${voucher.workflow_state}`);
    console.log(`   - status: ${voucher.status}`);
    console.log(`   - certified_by: ${voucher.certified_by}`);
    console.log(`   - is_resubmitted: ${voucher.is_resubmitted}`);
    
    console.log('\n3️⃣ WORKFLOW STATE MISMATCH ANALYSIS:');
    
    // Check if the workflow state matches expected resubmission states
    const expectedResubmissionStates = [
      'FINANCE_RESUBMISSION',
      'AUDIT_NEW_RESUBMITTED', 
      'AUDIT_DISPATCHED',
      'FINANCE_RESUBMISSION_RECEIVED'
    ];
    
    const isInResubmissionWorkflow = expectedResubmissionStates.includes(voucher.workflow_state);
    console.log(`   - Is in resubmission workflow: ${isInResubmissionWorkflow ? '✅ YES' : '❌ NO'}`);
    
    if (!isInResubmissionWorkflow) {
      console.log(`   - Current state "${voucher.workflow_state}" is NOT in expected resubmission states`);
      console.log('   - This explains why it\'s not appearing in tabs!');
    }
    
    console.log('\n4️⃣ TAB MAPPING ANALYSIS:');
    
    // Check what the current STATE_TO_TAB_MAPPING would return
    const STATE_TO_TAB_MAPPING = {
      'FINANCE_PENDING': 'pending',
      'FINANCE_PROCESSING': 'processing',
      'FINANCE_CERTIFIED': 'certified',
      'FINANCE_REJECTED': 'rejected',
      'FINANCE_RETURNED': 'returned',
      'FINANCE_RESUBMISSION_RECEIVED': 'certified', // Our fix
      'AUDIT_NEW': 'new-vouchers',
      'AUDIT_NEW_RESUBMITTED': 'new-vouchers',
      'AUDIT_PENDING_DISPATCH': 'pending-dispatch',
      'AUDIT_DISPATCHED': 'dispatched'
    };
    
    const financeTab = STATE_TO_TAB_MAPPING[voucher.workflow_state];
    console.log(`   - Finance tab mapping: ${financeTab || 'UNMAPPED'}`);
    
    // Check dual tab logic for Audit
    const auditTab = voucher.workflow_state === 'FINANCE_RESUBMISSION_RECEIVED' ? 'dispatched' : 
                    (voucher.workflow_state.startsWith('AUDIT_') ? STATE_TO_TAB_MAPPING[voucher.workflow_state] : null);
    console.log(`   - Audit tab mapping: ${auditTab || 'UNMAPPED'}`);
    
    console.log('\n5️⃣ ROOT CAUSE ANALYSIS:');
    console.log('========================');
    
    if (voucher.workflow_state === 'FINANCE_RESUBMISSION_RECEIVED') {
      console.log('✅ Voucher has correct workflow state for certified resubmission');
      console.log('✅ Our tab mappings should work');
      console.log('❓ Issue might be in client-side filtering or API calls');
    } else {
      console.log('❌ Voucher does NOT have expected workflow state');
      console.log(`   Expected: FINANCE_RESUBMISSION_RECEIVED`);
      console.log(`   Actual: ${voucher.workflow_state}`);
      console.log('❓ Voucher might not have gone through proper resubmission workflow');
    }
    
    console.log('\n6️⃣ NEXT INVESTIGATION STEPS:');
    console.log('=============================');
    console.log('1. Check if voucher went through complete resubmission workflow');
    console.log('2. Verify batch receiving process updated workflow_state correctly');
    console.log('3. Check if client-side filtering is working with correct API data');
    console.log('4. Test with a fresh resubmission workflow from start to finish');
    
  } else {
    console.log('❌ FINJUL0001 not found in database');
  }
  
  // Check all vouchers with resubmission-related states
  console.log('\n📊 ALL RESUBMISSION-RELATED VOUCHERS:');
  console.log('====================================');
  
  const [allResubmissions] = await connection.execute(`
    SELECT voucher_id, status, workflow_state, certified_by, is_resubmitted
    FROM vouchers 
    WHERE workflow_state IN (
      'FINANCE_RESUBMISSION',
      'AUDIT_NEW_RESUBMITTED', 
      'AUDIT_DISPATCHED',
      'FINANCE_RESUBMISSION_RECEIVED'
    ) OR is_resubmitted = 1
    ORDER BY created_at DESC
  `);
  
  if (allResubmissions.length > 0) {
    allResubmissions.forEach(v => {
      console.log(`   ${v.voucher_id}: ${v.workflow_state} | certified_by: ${v.certified_by || 'NULL'} | is_resubmitted: ${v.is_resubmitted}`);
    });
  } else {
    console.log('   No vouchers found with resubmission workflow states');
  }
  
  await connection.end();
}

analyzeResubmissionWorkflow().catch(console.error);
