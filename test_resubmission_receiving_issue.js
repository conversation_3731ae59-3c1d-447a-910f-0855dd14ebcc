const mysql = require('mysql2/promise');
const { v4: uuidv4 } = require('uuid');

async function testResubmissionReceivingIssue() {
  console.log('🔍 TESTING RESUBMISSION RECEIVING ISSUE');
  console.log('=====================================');
  
  const connection = await mysql.createConnection({
    host: 'localhost',
    user: 'root',
    password: 'vms@2025@1989',
    database: 'vms_production'
  });
  
  try {
    // Create a resubmission voucher scenario
    const voucherId = uuidv4();
    const voucherCode = 'RESUB_TEST_' + Date.now();
    
    console.log('1️⃣ Creating resubmission voucher in Audit DISPATCHED state...');
    
    // Create resubmission voucher that has been processed by Audit and dispatched
    await connection.execute(`
      INSERT INTO vouchers (
        id, voucher_id, date, claimant, description, amount, currency,
        department, original_department, status, workflow_state,
        created_by, created_at, is_resubmitted, rejected_by, rejection_time,
        comment, work_started, dispatched_by, dispatch_time
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), ?, ?, NOW(), ?, ?, ?, NOW())
    `, [
      voucherId, voucherCode, '2025-01-21', 'Resubmission Test', 'Testing resubmission receiving',
      2000.00, 'GHS', 'AUDIT', 'FINANCE', 'VOUCHER CERTIFIED', 'AUDIT_DISPATCHED',
      'TEST USER', 1, 'AUDIT USER', 'Missing documents - now provided', 1, 'AUDIT USER'
    ]);
    
    console.log(`✅ Created resubmission voucher: ${voucherCode}`);
    
    // Create batch for this voucher (Audit sending to Finance)
    const batchId = uuidv4();
    await connection.execute(`
      INSERT INTO voucher_batches (
        id, department, sent_by, sent_time, received, from_audit
      ) VALUES (?, ?, ?, NOW(), ?, ?)
    `, [batchId, 'FINANCE', 'AUDIT USER', false, true]);
    
    // Add voucher to batch
    await connection.execute(`
      INSERT INTO batch_vouchers (batch_id, voucher_id) VALUES (?, ?)
    `, [batchId, voucherId]);
    
    // Update voucher with batch
    await connection.execute(`
      UPDATE vouchers SET batch_id = ? WHERE id = ?
    `, [batchId, voucherId]);
    
    console.log('✅ Voucher added to batch for Finance receiving');
    
    // Check current state
    const [beforeResult] = await connection.execute(`
      SELECT voucher_id, status, workflow_state, department, is_resubmitted, dispatched_by
      FROM vouchers WHERE id = ?
    `, [voucherId]);
    
    const before = beforeResult[0];
    console.log('\n📊 BEFORE Finance receives:');
    console.log('   voucher_id:', before.voucher_id);
    console.log('   status:', before.status);
    console.log('   workflow_state:', before.workflow_state);
    console.log('   department:', before.department);
    console.log('   is_resubmitted:', before.is_resubmitted);
    console.log('   dispatched_by:', before.dispatched_by);
    
    // Expected: Should be visible in Audit DISPATCHED tab
    console.log('   ✅ Should appear in: Audit DISPATCHED tab');
    
    console.log('\n2️⃣ Simulating Finance receiving the batch...');
    
    // Simulate Finance receiving the voucher (this is where the issue occurs)
    // This mimics what happens when Finance clicks "Accept" in batch receiving
    await connection.execute(`
      UPDATE vouchers SET 
        status = 'VOUCHER CERTIFIED',
        workflow_state = 'FINANCE_PROCESSING',
        department = 'FINANCE',
        received_by = 'FINANCE USER',
        receipt_time = NOW(),
        department_receipt_time = NOW(),
        department_received_by = 'FINANCE USER'
      WHERE id = ?
    `, [voucherId]);
    
    // Mark batch as received
    await connection.execute(`
      UPDATE voucher_batches SET received = TRUE WHERE id = ?
    `, [batchId]);
    
    console.log('✅ Finance has received the voucher');
    
    // Check state after receiving
    const [afterResult] = await connection.execute(`
      SELECT voucher_id, status, workflow_state, department, is_resubmitted, 
             dispatched_by, received_by, department_received_by
      FROM vouchers WHERE id = ?
    `, [voucherId]);
    
    const after = afterResult[0];
    console.log('\n📊 AFTER Finance receives:');
    console.log('   voucher_id:', after.voucher_id);
    console.log('   status:', after.status);
    console.log('   workflow_state:', after.workflow_state);
    console.log('   department:', after.department);
    console.log('   is_resubmitted:', after.is_resubmitted);
    console.log('   dispatched_by:', after.dispatched_by);
    console.log('   received_by:', after.received_by);
    console.log('   department_received_by:', after.department_received_by);
    
    // Analyze the issues
    console.log('\n🔍 ISSUE ANALYSIS:');
    console.log('==================');
    
    // Issue 1: Finance CERTIFIED tab
    const shouldBeInFinanceCertified = after.status === 'VOUCHER CERTIFIED' && 
                                      after.workflow_state === 'FINANCE_CERTIFIED' &&
                                      after.department === 'FINANCE' &&
                                      after.is_resubmitted === 1;
    
    console.log('❌ ISSUE 1: Finance CERTIFIED Tab');
    console.log(`   Expected: workflow_state = 'FINANCE_CERTIFIED'`);
    console.log(`   Actual: workflow_state = '${after.workflow_state}'`);
    console.log(`   Result: ${shouldBeInFinanceCertified ? 'CORRECT' : 'INCORRECT - Voucher will NOT appear in Finance CERTIFIED tab'}`);
    
    // Issue 2: Audit DISPATCHED tab (should still show the voucher)
    console.log('\n❌ ISSUE 2: Audit DISPATCHED Tab');
    console.log('   Expected: Voucher copy should remain visible in Audit DISPATCHED tab');
    console.log('   Actual: Voucher department changed to FINANCE, so it disappears from Audit tabs');
    console.log('   Result: INCORRECT - Audit loses visibility of dispatched voucher');
    
    console.log('\n🎯 REQUIRED FIXES:');
    console.log('==================');
    console.log('1. When Finance receives a resubmission voucher:');
    console.log('   - Set workflow_state = "FINANCE_CERTIFIED" (not "FINANCE_PROCESSING")');
    console.log('   - Keep status = "VOUCHER CERTIFIED"');
    console.log('   - Keep is_resubmitted = 1');
    console.log('');
    console.log('2. Maintain Audit visibility:');
    console.log('   - Create/maintain a copy in Audit with workflow_state = "AUDIT_DISPATCHED"');
    console.log('   - Or use a different approach to keep audit visibility');
    
    // Clean up
    await connection.execute('DELETE FROM batch_vouchers WHERE batch_id = ?', [batchId]);
    await connection.execute('DELETE FROM voucher_batches WHERE id = ?', [batchId]);
    await connection.execute('DELETE FROM vouchers WHERE id = ?', [voucherId]);
    console.log('\n🧹 Test data cleaned up');
    
  } catch (error) {
    console.error('❌ Error testing resubmission receiving issue:', error);
  } finally {
    await connection.end();
  }
}

testResubmissionReceivingIssue().catch(console.error);
