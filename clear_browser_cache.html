<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Clear Browser Cache - VMS System</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        h1 {
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        .success {
            background: rgba(34, 197, 94, 0.2);
            border: 2px solid #22c55e;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        .info {
            background: rgba(59, 130, 246, 0.2);
            border: 2px solid #3b82f6;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        .steps {
            background: rgba(168, 85, 247, 0.2);
            border: 2px solid #a855f7;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        button {
            background: linear-gradient(45deg, #22c55e, #16a34a);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            margin: 10px;
            transition: all 0.3s ease;
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }
        .center {
            text-align: center;
        }
        ul {
            text-align: left;
        }
        li {
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧹 VMS System Cleaned Successfully!</h1>
        
        <div class="success">
            <h2>✅ Database Cleanup Complete</h2>
            <ul>
                <li><strong>8 vouchers</strong> removed</li>
                <li><strong>2 batches</strong> removed</li>
                <li><strong>2 batch-voucher links</strong> removed</li>
                <li><strong>4 notifications</strong> removed</li>
                <li><strong>1,222 audit log entries</strong> removed</li>
            </ul>
        </div>

        <div class="info">
            <h2>🔄 Clear Browser Cache Now</h2>
            <p>To ensure you see the clean system, please clear your browser cache using one of these methods:</p>
        </div>

        <div class="steps">
            <h3>Quick Cache Clear Options:</h3>
            <ul>
                <li><strong>Chrome/Edge:</strong> Press <code>Ctrl + Shift + Delete</code></li>
                <li><strong>Firefox:</strong> Press <code>Ctrl + Shift + Delete</code></li>
                <li><strong>Hard Refresh:</strong> Press <code>Ctrl + F5</code> on the VMS page</li>
                <li><strong>Incognito/Private:</strong> Open VMS in a private browsing window</li>
            </ul>
        </div>

        <div class="center">
            <button onclick="clearLocalStorage()">Clear Local Storage</button>
            <button onclick="clearSessionStorage()">Clear Session Storage</button>
            <button onclick="clearAllStorage()">Clear All Browser Data</button>
        </div>

        <div class="info">
            <h2>🚀 Ready for Testing</h2>
            <p>Your VMS system is now completely clean and ready for:</p>
            <ul>
                <li>✅ Testing rejection workflows</li>
                <li>✅ Testing resubmission workflows</li>
                <li>✅ Verifying badge logic</li>
                <li>✅ Testing batch receiving</li>
                <li>✅ Creating fresh vouchers</li>
            </ul>
        </div>

        <div class="center">
            <button onclick="goToVMS()">🎯 Go to Clean VMS System</button>
        </div>
    </div>

    <script>
        function clearLocalStorage() {
            localStorage.clear();
            alert('✅ Local Storage cleared!');
        }

        function clearSessionStorage() {
            sessionStorage.clear();
            alert('✅ Session Storage cleared!');
        }

        function clearAllStorage() {
            localStorage.clear();
            sessionStorage.clear();
            
            // Clear IndexedDB
            if ('indexedDB' in window) {
                indexedDB.databases().then(databases => {
                    databases.forEach(db => {
                        indexedDB.deleteDatabase(db.name);
                    });
                });
            }
            
            // Clear cookies for this domain
            document.cookie.split(";").forEach(function(c) { 
                document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/"); 
            });
            
            alert('✅ All browser storage cleared! Please refresh the page.');
        }

        function goToVMS() {
            // Clear everything first
            clearAllStorage();
            
            // Redirect to VMS
            setTimeout(() => {
                window.location.href = 'http://localhost:8080';
            }, 1000);
        }

        // Auto-clear on page load
        window.onload = function() {
            console.log('🧹 VMS System Cache Cleaner Loaded');
            console.log('✅ Database is clean and ready');
            console.log('🔄 Use the buttons above to clear browser cache');
        };
    </script>
</body>
</html>
