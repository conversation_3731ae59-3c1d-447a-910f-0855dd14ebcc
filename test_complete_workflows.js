const mysql = require('mysql2/promise');

async function testCompleteWorkflows() {
  console.log('🧪 TESTING COMPLETE REJECTION & RESUBMISSION WORKFLOWS');
  console.log('=====================================================');
  
  const connection = await mysql.createConnection({
    host: 'localhost',
    user: 'root',
    password: 'vms@2025@1989',
    database: 'vms_production'
  });
  
  try {
    // Create test voucher
    console.log('\n1️⃣ Creating test voucher...');
    const testVoucherId = 'WORKFLOW' + Date.now();
    const { v4: uuidv4 } = require('uuid');
    const testId = uuidv4();

    await connection.execute(`
      INSERT INTO vouchers (
        id, voucher_id, date, claimant, description, amount, currency,
        department, original_department, status, workflow_state,
        created_by, created_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
    `, [
      testId, testVoucherId, '2025-01-21', 'Test Claimant', 'Complete workflow test',
      1000.00, 'GHS', 'FINANCE', 'FINANCE', 'PENDING', 'FINANCE_PENDING',
      'TEST USER'
    ]);
    
    console.log(`✅ Created voucher: ${testVoucherId}`);
    
    // NORMAL WORKFLOW: Finance → Audit → Reject
    console.log('\n📋 TESTING NORMAL WORKFLOW TO REJECTION:');
    
    // Step 1: Finance sends to Audit
    console.log('\n2️⃣ Finance sends to Audit...');
    await connection.execute(`
      UPDATE vouchers SET 
        status = 'PENDING RECEIPT',
        workflow_state = 'FINANCE_PROCESSING',
        sent_to_audit = 1,
        department = 'FINANCE'
      WHERE id = ?
    `, [testId]);
    console.log('✅ Finance PROCESSING tab');
    
    // Step 2: Audit receives voucher
    console.log('\n3️⃣ Audit receives voucher...');
    await connection.execute(`
      UPDATE vouchers SET 
        status = 'VOUCHER PROCESSING',
        workflow_state = 'AUDIT_NEW',
        department = 'AUDIT'
      WHERE id = ?
    `, [testId]);
    console.log('✅ Audit NEW VOUCHERS tab');
    
    // Step 3: Audit rejects voucher
    console.log('\n4️⃣ Audit rejects voucher...');
    await connection.execute(`
      UPDATE vouchers SET 
        status = 'VOUCHER REJECTED',
        workflow_state = 'AUDIT_PENDING_DISPATCH_REJECTED',
        rejected_by = 'AUDIT USER',
        rejection_time = NOW(),
        comment = 'Missing documents for testing',
        work_started = 1
      WHERE id = ?
    `, [testId]);
    console.log('✅ Audit PENDING DISPATCH tab (rejected)');
    
    // Step 4: Audit dispatches rejected voucher to Finance
    console.log('\n5️⃣ Audit dispatches rejected voucher to Finance...');
    await connection.execute(`
      UPDATE vouchers SET 
        workflow_state = 'FINANCE_REJECTED',
        department = 'FINANCE'
      WHERE id = ?
    `, [testId]);
    console.log('✅ Finance REJECTED tab');
    
    // RESUBMISSION WORKFLOW: Add Back → Follow Normal Path
    console.log('\n📋 TESTING RESUBMISSION WORKFLOW:');
    
    // Step 5: Finance adds back (resubmission)
    console.log('\n6️⃣ Finance adds voucher back (resubmission)...');
    await connection.execute(`
      UPDATE vouchers SET 
        status = 'PENDING SUBMISSION',
        workflow_state = 'FINANCE_PENDING',
        is_resubmitted = 1,
        sent_to_audit = 0,
        batch_id = NULL
      WHERE id = ?
    `, [testId]);
    console.log('✅ Finance PENDING tab (RESUBMISSION badge)');
    
    // Step 6: Finance sends resubmission to Audit
    console.log('\n7️⃣ Finance sends resubmission to Audit...');
    await connection.execute(`
      UPDATE vouchers SET 
        status = 'PENDING RECEIPT',
        workflow_state = 'FINANCE_PROCESSING',
        sent_to_audit = 1,
        department = 'FINANCE'
      WHERE id = ?
    `, [testId]);
    console.log('✅ Finance PROCESSING tab (RESUBMISSION badge)');
    
    // Step 7: Audit receives resubmission
    console.log('\n8️⃣ Audit receives resubmission...');
    await connection.execute(`
      UPDATE vouchers SET 
        status = 'VOUCHER PROCESSING',
        workflow_state = 'AUDIT_NEW_RESUBMITTED',
        department = 'AUDIT'
      WHERE id = ?
    `, [testId]);
    console.log('✅ Audit NEW VOUCHERS tab (RESUBMISSION badge)');
    
    // Step 8: Audit starts work on resubmission
    console.log('\n9️⃣ Audit starts work on resubmission...');
    await connection.execute(`
      UPDATE vouchers SET 
        status = 'AUDIT PROCESSING',
        workflow_state = 'AUDIT_PENDING_DISPATCH',
        work_started = 1
      WHERE id = ?
    `, [testId]);
    console.log('✅ Audit PENDING DISPATCH tab (RESUBMISSION badge)');
    
    // Step 9: Audit dispatches resubmission to Finance
    console.log('\n🔟 Audit dispatches resubmission to Finance...');
    await connection.execute(`
      UPDATE vouchers SET 
        workflow_state = 'AUDIT_DISPATCHED',
        dispatched_by = 'AUDIT USER',
        dispatch_time = NOW()
      WHERE id = ?
    `, [testId]);
    console.log('✅ Audit DISPATCHED tab (RESUBMISSION badge)');
    
    // Step 10: Finance receives and certifies resubmission
    console.log('\n1️⃣1️⃣ Finance receives and certifies resubmission...');
    await connection.execute(`
      UPDATE vouchers SET
        status = 'VOUCHER CERTIFIED',
        workflow_state = 'FINANCE_CERTIFIED',
        department = 'FINANCE',
        certified_by = 'FINANCE USER',
        certified_time = NOW()
      WHERE id = ?
    `, [testId]);
    console.log('✅ Finance CERTIFIED tab (CERTIFIED-RESUBMISSION badge)');
    
    // Final verification
    const [finalState] = await connection.execute(`
      SELECT voucher_id, status, workflow_state, department, is_resubmitted, 
             rejected_by, comment, certified_by
      FROM vouchers WHERE id = ?
    `, [testId]);
    
    console.log('\n📊 FINAL STATE VERIFICATION:');
    const voucher = finalState[0];
    console.log('   voucher_id:', voucher.voucher_id);
    console.log('   status:', voucher.status);
    console.log('   workflow_state:', voucher.workflow_state);
    console.log('   department:', voucher.department);
    console.log('   is_resubmitted:', voucher.is_resubmitted);
    console.log('   rejected_by:', voucher.rejected_by);
    console.log('   comment:', voucher.comment);
    console.log('   certified_by:', voucher.certified_by);
    
    // Test badge logic
    console.log('\n🏷️ BADGE LOGIC TEST:');
    const isResubmission = voucher.is_resubmitted === 1;
    const isCertified = voucher.certified_by !== null;
    const expectedBadge = isResubmission && isCertified ? 'CERTIFIED-RESUBMISSION' : 
                         (isResubmission ? 'RESUBMISSION' : 'NORMAL');
    
    console.log('   Expected Badge:', expectedBadge);
    console.log('   Badge Logic Working:', expectedBadge === 'CERTIFIED-RESUBMISSION' ? '✅ YES' : '❌ NO');
    
    // Test tab visibility
    console.log('\n📂 TAB VISIBILITY TEST:');
    const financeVisible = voucher.workflow_state === 'FINANCE_CERTIFIED' && voucher.department === 'FINANCE';
    const auditVisible = voucher.workflow_state === 'FINANCE_CERTIFIED'; // Should also appear in Audit DISPATCHED
    
    console.log('   Finance CERTIFIED tab:', financeVisible ? '✅ VISIBLE' : '❌ NOT VISIBLE');
    console.log('   Audit DISPATCHED tab:', auditVisible ? '✅ VISIBLE' : '❌ NOT VISIBLE');
    
    if (expectedBadge === 'CERTIFIED-RESUBMISSION' && financeVisible) {
      console.log('\n🎉 COMPLETE WORKFLOW SUCCESS!');
      console.log('✅ Rejection workflow works correctly');
      console.log('✅ Resubmission follows normal workflow path');
      console.log('✅ Badges display correctly throughout');
      console.log('✅ Final state shows in correct tabs');
    } else {
      console.log('\n❌ WORKFLOW HAS ISSUES!');
    }
    
    // Clean up test voucher
    await connection.execute('DELETE FROM vouchers WHERE id = ?', [testId]);
    console.log('\n🧹 Test voucher cleaned up');
    
  } catch (error) {
    console.error('❌ Error testing workflows:', error);
  } finally {
    await connection.end();
  }
}

testCompleteWorkflows().catch(console.error);
