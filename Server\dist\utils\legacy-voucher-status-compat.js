"use strict";
/**
 * Legacy Voucher Status Compatibility Layer
 * Temporary compatibility layer for legacy code during migration
 * TODO: Remove this file once all legacy code is migrated to workflow state machine
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.VOUCHER_STATUSES = void 0;
exports.synchronizeVoucherFlags = synchronizeVoucherFlags;
exports.isValidStatusTransition = isValidStatusTransition;
exports.isValidStatusTransition_old = isValidStatusTransition_old;
exports.mapLegacyStatusToWorkflowState = mapLegacyStatusToWorkflowState;
exports.mapWorkflowStateToLegacyStatus = mapWorkflowStateToLegacyStatus;
exports.updateVoucherWithBothSystems = updateVoucherWithBothSystems;
exports.warnLegacyUsage = warnLegacyUsage;
const VoucherWorkflowStateMachine_1 = require("../workflow/VoucherWorkflowStateMachine");
// Legacy status constants for backward compatibility
exports.VOUCHER_STATUSES = {
    PENDING: 'PENDING SUBMISSION',
    PENDING_RECEIPT: 'PENDING RECEIPT',
    VOUCHER_PROCESSING: 'VOUCHER PROCESSING',
    AUDIT_PROCESSING: 'AUDIT: PROCESSING',
    VOUCHER_CERTIFIED: 'VOUCHER CERTIFIED',
    VOUCHER_REJECTED: 'VOUCHER REJECTED',
    VOUCHER_RETURNED: 'VOUCHER RETURNED',
    PENDING_DISPATCH: 'PENDING DISPATCH',
    DISPATCHED: 'DISPATCHED'
};
// Legacy flag synchronization (simplified)
function synchronizeVoucherFlags(options) {
    const { status } = options;
    // Map legacy status to workflow flags
    const flags = {
        isNew: status === exports.VOUCHER_STATUSES.PENDING,
        isPendingDispatch: status === exports.VOUCHER_STATUSES.PENDING_DISPATCH,
        isDispatched: status === exports.VOUCHER_STATUSES.DISPATCHED,
        isCertified: status === exports.VOUCHER_STATUSES.VOUCHER_CERTIFIED,
        isRejected: status === exports.VOUCHER_STATUSES.VOUCHER_REJECTED,
        isReturned: status === exports.VOUCHER_STATUSES.VOUCHER_RETURNED,
        isProcessing: status === exports.VOUCHER_STATUSES.VOUCHER_PROCESSING || status === exports.VOUCHER_STATUSES.AUDIT_PROCESSING
    };
    return { flags };
}
// Legacy status transition validation (simplified)
function isValidStatusTransition(fromStatus, toStatus, userRole, voucher) {
    // For now, allow all transitions during migration
    // TODO: Implement proper validation using workflow state machine
    console.warn('⚠️  Using legacy status transition validation - migrate to workflow state machine');
    // Basic validation rules
    const validTransitions = {
        [exports.VOUCHER_STATUSES.PENDING]: [exports.VOUCHER_STATUSES.PENDING_RECEIPT],
        [exports.VOUCHER_STATUSES.PENDING_RECEIPT]: [exports.VOUCHER_STATUSES.VOUCHER_PROCESSING, exports.VOUCHER_STATUSES.VOUCHER_RETURNED],
        [exports.VOUCHER_STATUSES.VOUCHER_PROCESSING]: [exports.VOUCHER_STATUSES.VOUCHER_CERTIFIED, exports.VOUCHER_STATUSES.VOUCHER_REJECTED, exports.VOUCHER_STATUSES.VOUCHER_RETURNED],
        [exports.VOUCHER_STATUSES.AUDIT_PROCESSING]: [exports.VOUCHER_STATUSES.PENDING_DISPATCH],
        [exports.VOUCHER_STATUSES.PENDING_DISPATCH]: [exports.VOUCHER_STATUSES.DISPATCHED],
        [exports.VOUCHER_STATUSES.VOUCHER_REJECTED]: [exports.VOUCHER_STATUSES.VOUCHER_PROCESSING],
        [exports.VOUCHER_STATUSES.VOUCHER_RETURNED]: [exports.VOUCHER_STATUSES.VOUCHER_PROCESSING]
    };
    const allowedTransitions = validTransitions[fromStatus] || [];
    const isValid = allowedTransitions.includes(toStatus);
    return {
        isValid,
        reason: isValid ? undefined : `Transition from ${fromStatus} to ${toStatus} not allowed`
    };
}
// Legacy status transition validation (backward compatibility)
function isValidStatusTransition_old(fromStatus, toStatus, userRole, voucher) {
    const result = isValidStatusTransition(fromStatus, toStatus, userRole, voucher);
    return result.isValid;
}
// Map legacy status to workflow state
function mapLegacyStatusToWorkflowState(legacyStatus) {
    const mapping = {
        [exports.VOUCHER_STATUSES.PENDING]: VoucherWorkflowStateMachine_1.WorkflowState.FINANCE_PENDING,
        [exports.VOUCHER_STATUSES.PENDING_RECEIPT]: VoucherWorkflowStateMachine_1.WorkflowState.FINANCE_PROCESSING,
        [exports.VOUCHER_STATUSES.VOUCHER_PROCESSING]: VoucherWorkflowStateMachine_1.WorkflowState.AUDIT_NEW,
        [exports.VOUCHER_STATUSES.AUDIT_PROCESSING]: VoucherWorkflowStateMachine_1.WorkflowState.AUDIT_PENDING_DISPATCH,
        [exports.VOUCHER_STATUSES.PENDING_DISPATCH]: VoucherWorkflowStateMachine_1.WorkflowState.AUDIT_PENDING_DISPATCH,
        [exports.VOUCHER_STATUSES.DISPATCHED]: VoucherWorkflowStateMachine_1.WorkflowState.AUDIT_DISPATCHED,
        [exports.VOUCHER_STATUSES.VOUCHER_CERTIFIED]: VoucherWorkflowStateMachine_1.WorkflowState.FINANCE_CERTIFIED,
        [exports.VOUCHER_STATUSES.VOUCHER_REJECTED]: VoucherWorkflowStateMachine_1.WorkflowState.FINANCE_REJECTED,
        [exports.VOUCHER_STATUSES.VOUCHER_RETURNED]: VoucherWorkflowStateMachine_1.WorkflowState.FINANCE_RETURNED
    };
    return mapping[legacyStatus] || VoucherWorkflowStateMachine_1.WorkflowState.FINANCE_PENDING;
}
// Map workflow state to legacy status
function mapWorkflowStateToLegacyStatus(workflowState) {
    const mapping = {
        [VoucherWorkflowStateMachine_1.WorkflowState.FINANCE_PENDING]: exports.VOUCHER_STATUSES.PENDING,
        [VoucherWorkflowStateMachine_1.WorkflowState.FINANCE_PROCESSING]: exports.VOUCHER_STATUSES.PENDING_RECEIPT,
        [VoucherWorkflowStateMachine_1.WorkflowState.FINANCE_CERTIFIED]: exports.VOUCHER_STATUSES.VOUCHER_CERTIFIED,
        [VoucherWorkflowStateMachine_1.WorkflowState.FINANCE_REJECTED]: exports.VOUCHER_STATUSES.VOUCHER_REJECTED,
        [VoucherWorkflowStateMachine_1.WorkflowState.FINANCE_RETURNED]: exports.VOUCHER_STATUSES.VOUCHER_RETURNED,
        [VoucherWorkflowStateMachine_1.WorkflowState.FINANCE_RESUBMISSION_RECEIVED]: exports.VOUCHER_STATUSES.VOUCHER_CERTIFIED, // Certified resubmissions
        [VoucherWorkflowStateMachine_1.WorkflowState.AUDIT_NEW]: exports.VOUCHER_STATUSES.VOUCHER_PROCESSING,
        [VoucherWorkflowStateMachine_1.WorkflowState.AUDIT_NEW_RESUBMITTED]: exports.VOUCHER_STATUSES.VOUCHER_PROCESSING,
        [VoucherWorkflowStateMachine_1.WorkflowState.AUDIT_PENDING_DISPATCH]: exports.VOUCHER_STATUSES.AUDIT_PROCESSING,
        [VoucherWorkflowStateMachine_1.WorkflowState.AUDIT_PENDING_DISPATCH_REJECTED]: exports.VOUCHER_STATUSES.AUDIT_PROCESSING,
        [VoucherWorkflowStateMachine_1.WorkflowState.AUDIT_PENDING_DISPATCH_RETURNED]: exports.VOUCHER_STATUSES.AUDIT_PROCESSING,
        [VoucherWorkflowStateMachine_1.WorkflowState.AUDIT_DISPATCHED]: exports.VOUCHER_STATUSES.DISPATCHED,
        [VoucherWorkflowStateMachine_1.WorkflowState.AUDIT_REJECTED_COPY]: exports.VOUCHER_STATUSES.VOUCHER_REJECTED,
        [VoucherWorkflowStateMachine_1.WorkflowState.AUDIT_RETURNED_COPY]: exports.VOUCHER_STATUSES.VOUCHER_RETURNED
    };
    return mapping[workflowState] || exports.VOUCHER_STATUSES.PENDING;
}
// Migration helper: Update voucher with both legacy status and workflow state
async function updateVoucherWithBothSystems(db, voucherId, workflowState, additionalFields = {}) {
    const legacyStatus = mapWorkflowStateToLegacyStatus(workflowState);
    const { flags } = synchronizeVoucherFlags({ status: legacyStatus });
    const updateFields = {
        status: legacyStatus,
        workflow_state: workflowState,
        flags: JSON.stringify(flags),
        last_modified: new Date(),
        ...additionalFields
    };
    const setClause = Object.keys(updateFields)
        .map(key => `${key} = ?`)
        .join(', ');
    const values = [...Object.values(updateFields), voucherId];
    await db.execute(`
    UPDATE vouchers 
    SET ${setClause}
    WHERE id = ?
  `, values);
    console.log(`✅ Updated voucher ${voucherId}: ${legacyStatus} (${workflowState})`);
}
// Deprecation warnings
function warnLegacyUsage(functionName, location) {
    console.warn(`⚠️  DEPRECATED: ${functionName} used in ${location}`);
    console.warn(`   Please migrate to workflow state machine`);
    console.warn(`   See: /workflow/VoucherWorkflowStateMachine.ts`);
}
//# sourceMappingURL=legacy-voucher-status-compat.js.map