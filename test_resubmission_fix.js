const mysql = require('mysql2/promise');

async function testResubmissionFix() {
  console.log('🧪 TESTING RESUBMISSION FIX');
  console.log('============================');
  
  const connection = await mysql.createConnection({
    host: 'localhost',
    user: 'root',
    password: 'vms@2025@1989',
    database: 'vms_production'
  });
  
  try {
    // Check current state of FINJUL0004
    const [vouchers] = await connection.execute(`
      SELECT id, voucher_id, status, workflow_state, is_resubmitted, 
             certified_by, rejected_by, rejection_time, finance_received
      FROM vouchers 
      WHERE voucher_id = 'FINJUL0004'
    `);
    
    if (vouchers.length === 0) {
      console.log('❌ FINJUL0004 not found');
      return;
    }
    
    const voucher = vouchers[0];
    console.log('📋 CURRENT STATE OF FINJUL0004:');
    console.log(`   Status: ${voucher.status}`);
    console.log(`   Workflow State: ${voucher.workflow_state}`);
    console.log(`   Is Resubmitted: ${voucher.is_resubmitted}`);
    console.log(`   Certified By: ${voucher.certified_by}`);
    console.log(`   Rejected By: ${voucher.rejected_by}`);
    console.log(`   Finance Received: ${voucher.finance_received}`);
    
    // Test the resubmission logic
    const isResubmittedAndCertified = (voucher.is_resubmitted === 1 || voucher.is_resubmitted === true) && 
                                     (voucher.certified_by && voucher.certified_by.trim() !== '');
    
    console.log('\n🔍 RESUBMISSION LOGIC TEST:');
    console.log(`   Has rejection history: ${voucher.rejected_by ? 'YES' : 'NO'}`);
    console.log(`   Is resubmitted: ${voucher.is_resubmitted === 1 ? 'YES' : 'NO'}`);
    console.log(`   Is certified: ${voucher.certified_by ? 'YES' : 'NO'}`);
    console.log(`   Should be treated as certified resubmission: ${isResubmittedAndCertified ? 'YES' : 'NO'}`);
    
    if (isResubmittedAndCertified) {
      console.log('\n✅ EXPECTED BEHAVIOR:');
      console.log('   - Should be processed as CERTIFIED, not REJECTED');
      console.log('   - Should appear in Finance CERTIFIED tab');
      console.log('   - Should remain in Audit DISPATCHED tab');
      console.log('   - Should have CERTIFIED-RESUBMISSION badge');
    } else {
      console.log('\n❌ ISSUE DETECTED:');
      if (voucher.is_resubmitted !== 1) {
        console.log('   - is_resubmitted flag is not set correctly');
      }
      if (!voucher.certified_by) {
        console.log('   - certified_by is not set');
      }
    }
    
    // Now test if we can create a new batch and receive it
    console.log('\n🧪 TESTING BATCH RECEIVING LOGIC:');
    
    // Simulate the batch receiving logic
    if (voucher.rejected_by && voucher.rejection_time && !voucher.is_rejection_copy) {
      if (isResubmittedAndCertified) {
        console.log('✅ FIXED LOGIC: Would process as CERTIFIED resubmission');
        console.log('   - Status would be: VOUCHER CERTIFIED');
        console.log('   - Workflow state would be: AUDIT_DISPATCHED');
        console.log('   - Finance received would be: 1');
      } else {
        console.log('❌ OLD LOGIC: Would process as REJECTED');
        console.log('   - Status would be: VOUCHER REJECTED');
        console.log('   - Workflow state would be: FINANCE_REJECTED');
        console.log('   - Finance received would be: 0');
      }
    }
    
    console.log('\n🎯 NEXT STEPS:');
    if (voucher.status === 'VOUCHER REJECTED' && voucher.workflow_state === 'FINANCE_REJECTED') {
      console.log('❌ The voucher was already processed with the old logic');
      console.log('   You need to create a new batch and dispatch it again to test the fix');
      console.log('   Or manually reset the voucher state for testing');
    } else if (voucher.status === 'VOUCHER PROCESSING') {
      console.log('✅ The voucher is ready for testing');
      console.log('   Dispatch it from Audit and then receive it in Finance to test the fix');
    } else {
      console.log('ℹ️  Current status requires manual review');
    }
    
  } catch (error) {
    console.error('❌ Error testing resubmission fix:', error);
  } finally {
    await connection.end();
  }
}

testResubmissionFix().catch(console.error);
