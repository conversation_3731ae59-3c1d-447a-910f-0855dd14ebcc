/**
 * VMS Voucher Workflow Manager
 * Handles atomic state transitions and voucher copy management
 */

import { 
  WorkflowState, 
  WorkflowEvent, 
  BadgeType,
  VoucherWorkflowContext,
  VoucherWorkflowStateMachine 
} from './VoucherWorkflowStateMachine';

export interface VoucherWorkflowResult {
  success: boolean;
  voucher: any;
  copy?: any;
  previousState: WorkflowState;
  newState: WorkflowState;
  affectedTabs: string[];
  error?: string;
}

export interface WorkflowEventData {
  type: WorkflowEvent;
  voucherId: string;
  context: VoucherWorkflowContext;
  data?: Record<string, any>;
}

export class VoucherWorkflowManager {
  private db: any;
  private eventPublisher: any;
  private auditLogger: any;

  constructor(db: any, eventPublisher: any, auditLogger: any) {
    this.db = db;
    this.eventPublisher = eventPublisher;
    this.auditLogger = auditLogger;
  }

  /**
   * Execute workflow transition atomically
   */
  async transitionVoucher(eventData: WorkflowEventData): Promise<VoucherWorkflowResult> {
    const { type: event, voucherId, context } = eventData;

    return await this.withTransaction(async (tx) => {
      // 1. Load current voucher
      const currentVoucher = await this.getVoucherById(voucherId, tx);
      if (!currentVoucher) {
        throw new Error(`Voucher ${voucherId} not found`);
      }

      const currentState = currentVoucher.workflow_state as WorkflowState;

      // 2. Validate transition
      const transition = VoucherWorkflowStateMachine.getTransition(currentState, event);
      if (!transition) {
        throw new Error(
          `Invalid transition from ${currentState} with event ${event}`
        );
      }

      // 3. Calculate new state and properties
      const newState = transition.toState;
      const badge = transition.badge || BadgeType.NONE;
      const affectedTabs = this.getAffectedTabs(currentState, newState, context);

      // 4. Create copy if required (for rejection/return flows)
      let copy = null;
      if (transition.requiresCopy && transition.copyState) {
        copy = await this.createVoucherCopy(currentVoucher, transition.copyState, tx);
      }

      // 5. Update original voucher
      const updatedVoucher = await this.updateVoucherState(
        voucherId,
        {
          workflow_state: newState,
          badge_type: badge,
          department: this.getDepartmentForState(newState, currentVoucher),
          version: currentVoucher.version + 1,
          last_modified: new Date()
        },
        tx
      );

      // 6. Log audit trail
      await this.logWorkflowTransition({
        voucherId,
        fromState: currentState,
        toState: newState,
        event,
        userId: context.userId,
        timestamp: context.timestamp,
        copyId: copy?.id
      }, tx);

      // 7. Publish real-time event
      await this.publishWorkflowEvent({
        type: 'VOUCHER_STATE_CHANGED',
        voucherId,
        previousState: currentState,
        newState,
        affectedDepartments: [currentVoucher.original_department, 'AUDIT'],
        affectedTabs,
        copy
      });

      return {
        success: true,
        voucher: updatedVoucher,
        copy,
        previousState: currentState,
        newState,
        affectedTabs
      };
    });
  }

  /**
   * Create voucher copy for rejection/return flows
   */
  private async createVoucherCopy(
    originalVoucher: any, 
    copyState: WorkflowState, 
    tx: any
  ): Promise<any> {
    const copyData = {
      ...originalVoucher,
      id: this.generateId(),
      voucher_id: `${originalVoucher.voucher_id}_COPY`,
      workflow_state: copyState,
      is_copy: true,
      parent_voucher_id: originalVoucher.id,
      created_at: new Date(),
      version: 1
    };

    delete copyData.version; // Will be set to 1 above

    const query = `
      INSERT INTO vouchers SET ?
    `;

    await this.db.execute(query, [copyData], { transaction: tx });
    return copyData;
  }

  /**
   * Update voucher state atomically
   */
  private async updateVoucherState(
    voucherId: string, 
    updates: Record<string, any>, 
    tx: any
  ): Promise<any> {
    const setClause = Object.keys(updates)
      .map(key => `${key} = ?`)
      .join(', ');

    const query = `
      UPDATE vouchers 
      SET ${setClause}
      WHERE id = ?
    `;

    const values = [...Object.values(updates), voucherId];
    await this.db.execute(query, values, { transaction: tx });

    // Return updated voucher
    return await this.getVoucherById(voucherId, tx);
  }

  /**
   * Get voucher by ID
   */
  private async getVoucherById(voucherId: string, tx?: any): Promise<any> {
    const query = `
      SELECT * FROM vouchers WHERE id = ?
    `;
    
    const options = tx ? { transaction: tx } : {};
    const [rows] = await this.db.execute(query, [voucherId], options);
    return rows[0] || null;
  }

  /**
   * Determine department based on workflow state
   */
  private getDepartmentForState(state: WorkflowState, voucher: any): string {
    if (state.startsWith('AUDIT_')) {
      return 'AUDIT';
    }
    if (state.startsWith('FINANCE_')) {
      return voucher.original_department;
    }
    return voucher.department;
  }

  /**
   * Get affected tabs for real-time updates
   */
  private getAffectedTabs(
    fromState: WorkflowState, 
    toState: WorkflowState, 
    context: VoucherWorkflowContext
  ): string[] {
    const tabs: string[] = [];

    // Add tabs for both states
    const fromTab = VoucherWorkflowStateMachine.getTabForVoucher(fromState, context.userDepartment);
    const toTab = VoucherWorkflowStateMachine.getTabForVoucher(toState, context.userDepartment);

    if (fromTab) tabs.push(fromTab);
    if (toTab && toTab !== fromTab) tabs.push(toTab);

    // Add tabs for other affected departments
    const auditFromTab = VoucherWorkflowStateMachine.getTabForVoucher(fromState, 'AUDIT');
    const auditToTab = VoucherWorkflowStateMachine.getTabForVoucher(toState, 'AUDIT');

    if (auditFromTab && !tabs.includes(auditFromTab)) tabs.push(auditFromTab);
    if (auditToTab && !tabs.includes(auditToTab)) tabs.push(auditToTab);

    return tabs;
  }

  /**
   * Log workflow transition for audit trail
   */
  private async logWorkflowTransition(data: any, tx: any): Promise<void> {
    const query = `
      INSERT INTO workflow_audit_log 
      (voucher_id, from_state, to_state, event_type, user_id, timestamp, copy_id)
      VALUES (?, ?, ?, ?, ?, ?, ?)
    `;

    await this.db.execute(query, [
      data.voucherId,
      data.fromState,
      data.toState,
      data.event,
      data.userId,
      data.timestamp,
      data.copyId || null
    ], { transaction: tx });
  }

  /**
   * Publish real-time workflow event
   */
  private async publishWorkflowEvent(eventData: any): Promise<void> {
    if (this.eventPublisher) {
      await this.eventPublisher.publish('workflow.state.changed', eventData);
    }
  }

  /**
   * Execute function within database transaction
   */
  private async withTransaction<T>(fn: (tx: any) => Promise<T>): Promise<T> {
    const connection = await this.db.getConnection();
    await connection.beginTransaction();

    try {
      const result = await fn(connection);
      await connection.commit();
      return result;
    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }
  }

  /**
   * Generate unique ID
   */
  private generateId(): string {
    return require('uuid').v4();
  }
}
