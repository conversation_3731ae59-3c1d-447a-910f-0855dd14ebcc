const mysql = require('mysql2/promise');
require('dotenv').config();

async function debugResubmissionIssue() {
  let connection;
  
  try {
    // Create connection
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || 'vms@2025@1989',
      database: process.env.DB_NAME || 'vms_production'
    });

    console.log('🔗 Connected to database');
    console.log('🔍 Debugging resubmission issue - checking current state...\n');

    // Check all recent vouchers
    const [vouchers] = await connection.execute(`
      SELECT 
        voucher_id, 
        claimant, 
        status, 
        workflow_state, 
        is_resubmitted, 
        sent_to_audit,
        batch_id,
        rejected_by,
        rejection_time,
        comment,
        date
      FROM vouchers 
      ORDER BY date DESC
      LIMIT 10
    `);

    console.log('📊 Recent Vouchers:');
    vouchers.forEach((voucher, index) => {
      console.log(`--- Voucher ${index + 1} ---`);
      console.log(`Voucher ID: ${voucher.voucher_id}`);
      console.log(`Claimant: ${voucher.claimant}`);
      console.log(`Status: ${voucher.status}`);
      console.log(`Workflow State: ${voucher.workflow_state || 'NULL'}`);
      console.log(`Is Resubmitted: ${voucher.is_resubmitted ? 'YES' : 'NO'}`);
      console.log(`Sent to Audit: ${voucher.sent_to_audit ? 'YES' : 'NO'}`);
      console.log(`Batch ID: ${voucher.batch_id || 'None'}`);
      console.log(`Rejected By: ${voucher.rejected_by || 'None'}`);
      console.log(`Rejection Time: ${voucher.rejection_time || 'None'}`);
      console.log(`Comment: ${voucher.comment || 'None'}`);
      console.log('');
    });

    // Check recent batches
    const [batches] = await connection.execute(`
      SELECT 
        id, 
        department,
        sent_by,
        sent_time,
        received,
        contains_resubmissions,
        resubmission_count
      FROM voucher_batches 
      ORDER BY sent_time DESC 
      LIMIT 5
    `);

    console.log('📦 Recent Batches:');
    batches.forEach((batch, index) => {
      console.log(`--- Batch ${index + 1} ---`);
      console.log(`Batch ID: ${batch.id}`);
      console.log(`Department: ${batch.department}`);
      console.log(`Sent By: ${batch.sent_by}`);
      console.log(`Sent Time: ${batch.sent_time}`);
      console.log(`Received: ${batch.received ? 'YES' : 'NO'}`);
      console.log(`Contains Resubmissions: ${batch.contains_resubmissions ? 'YES' : 'NO'}`);
      console.log(`Resubmission Count: ${batch.resubmission_count || 0}`);
      console.log('');
    });

    // Find vouchers that should be resubmissions
    const [potentialResubmissions] = await connection.execute(`
      SELECT 
        voucher_id, 
        claimant, 
        status, 
        is_resubmitted,
        rejected_by,
        rejection_time,
        sent_to_audit
      FROM vouchers 
      WHERE (rejected_by IS NOT NULL AND rejected_by != '') 
         OR (rejection_time IS NOT NULL)
      ORDER BY date DESC
    `);

    console.log('🔍 Potential Resubmissions (vouchers with rejection history):');
    potentialResubmissions.forEach((voucher, index) => {
      const shouldBeResubmission = voucher.sent_to_audit && (voucher.rejected_by || voucher.rejection_time);
      const actuallyMarked = voucher.is_resubmitted;
      const status = shouldBeResubmission === actuallyMarked ? '✅' : '❌';
      
      console.log(`${status} ${voucher.voucher_id} (${voucher.claimant})`);
      console.log(`   Status: ${voucher.status}`);
      console.log(`   Should be resubmission: ${shouldBeResubmission ? 'YES' : 'NO'}`);
      console.log(`   Actually marked: ${actuallyMarked ? 'YES' : 'NO'}`);
      console.log(`   Rejected by: ${voucher.rejected_by || 'None'}`);
      console.log(`   Sent to audit: ${voucher.sent_to_audit ? 'YES' : 'NO'}`);
      console.log('');
    });

    // Check batch-voucher relationships for recent batches
    if (batches.length > 0) {
      const latestBatchId = batches[0].id;
      console.log(`🔗 Checking vouchers in latest batch: ${latestBatchId}`);
      
      const [batchVouchers] = await connection.execute(`
        SELECT 
          bv.batch_id,
          bv.voucher_id as bv_voucher_id,
          v.voucher_id,
          v.claimant,
          v.status,
          v.is_resubmitted,
          v.rejected_by
        FROM batch_vouchers bv
        JOIN vouchers v ON bv.voucher_id = v.id
        WHERE bv.batch_id = ?
      `, [latestBatchId]);

      batchVouchers.forEach((bv, index) => {
        console.log(`   ${index + 1}. ${bv.voucher_id} (${bv.claimant})`);
        console.log(`      Status: ${bv.status}`);
        console.log(`      Is Resubmitted: ${bv.is_resubmitted ? 'YES' : 'NO'}`);
        console.log(`      Rejected By: ${bv.rejected_by || 'None'}`);
      });
    }

    console.log('\n🎯 ANALYSIS:');
    console.log('=============');
    
    const resubmittedVouchers = vouchers.filter(v => v.is_resubmitted);
    const resubmissionBatches = batches.filter(b => b.contains_resubmissions);
    
    console.log(`📊 Vouchers marked as resubmitted: ${resubmittedVouchers.length}`);
    console.log(`📦 Batches containing resubmissions: ${resubmissionBatches.length}`);
    
    if (resubmittedVouchers.length === 0) {
      console.log('❌ NO VOUCHERS MARKED AS RESUBMITTED');
      console.log('   This means the resubmission detection logic is still not working');
    }
    
    if (resubmissionBatches.length === 0) {
      console.log('❌ NO BATCHES MARKED AS CONTAINING RESUBMISSIONS');
      console.log('   This means batch metadata is not being set correctly');
    }

  } catch (error) {
    console.error('❌ Error debugging resubmission issue:', error);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n🔌 Database connection closed');
    }
  }
}

// Run the debug
debugResubmissionIssue();
