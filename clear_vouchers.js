const mysql = require('mysql2/promise');

async function clearAllVouchers() {
  let connection;
  try {
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root', 
      password: 'vms@2025@1989',
      database: 'vms_production'
    });
    
    console.log('🧹 CLEARING ALL VOUCHERS AND BATCHES');
    console.log('=====================================');
    
    // First, get counts before deletion
    const [voucherCount] = await connection.execute('SELECT COUNT(*) as count FROM vouchers');
    const [batchCount] = await connection.execute('SELECT COUNT(*) as count FROM voucher_batches');
    
    console.log(`📊 Before deletion:`);
    console.log(`   Vouchers: ${voucherCount[0].count}`);
    console.log(`   Batches: ${batchCount[0].count}`);
    
    // Delete all vouchers (this will also cascade to related records)
    await connection.execute('DELETE FROM vouchers');
    console.log('✅ All vouchers deleted');
    
    // Delete all batches
    await connection.execute('DELETE FROM voucher_batches');
    console.log('✅ All voucher batches deleted');
    
    // Reset auto-increment counters
    await connection.execute('ALTER TABLE vouchers AUTO_INCREMENT = 1');
    await connection.execute('ALTER TABLE voucher_batches AUTO_INCREMENT = 1');
    console.log('✅ Auto-increment counters reset');
    
    // Verify deletion
    const [finalVoucherCount] = await connection.execute('SELECT COUNT(*) as count FROM vouchers');
    const [finalBatchCount] = await connection.execute('SELECT COUNT(*) as count FROM voucher_batches');
    
    console.log(`\n📊 After deletion:`);
    console.log(`   Vouchers: ${finalVoucherCount[0].count}`);
    console.log(`   Batches: ${finalBatchCount[0].count}`);
    
    console.log(`\n🎉 DATABASE CLEARED SUCCESSFULLY!`);
    console.log(`\n🚀 Ready for fresh resubmission workflow testing:`);
    console.log(`   1. Create a new voucher`);
    console.log(`   2. Send to Audit`);
    console.log(`   3. Reject it`);
    console.log(`   4. Add back to pending`);
    console.log(`   5. Send to Audit again (should trigger resubmission logic)`);
    
  } catch (error) {
    console.error('❌ Error clearing vouchers:', error);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

clearAllVouchers();
