const mysql = require('mysql2/promise');
require('dotenv').config();

async function testManualUpdate() {
  let connection;
  
  try {
    // Create connection
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || 'vms@2025@1989',
      database: process.env.DB_NAME || 'vms_production'
    });

    console.log('🔗 Connected to database');
    console.log('🧪 TESTING MANUAL UPDATE - Simulating server logic\n');

    // Get the KAY voucher
    const [vouchers] = await connection.execute(`
      SELECT 
        id,
        voucher_id, 
        claimant, 
        status, 
        workflow_state, 
        is_resubmitted, 
        sent_to_audit,
        batch_id,
        rejected_by,
        rejection_time,
        comment
      FROM vouchers 
      WHERE claimant = 'KAY' AND voucher_id = 'FINJUL0001'
    `);

    if (vouchers.length === 0) {
      console.log('❌ No KAY voucher found');
      return;
    }

    const voucher = vouchers[0];
    console.log('📊 CURRENT VOUCHER STATE:');
    console.log(`ID: ${voucher.id}`);
    console.log(`Status: ${voucher.status}`);
    console.log(`Workflow State: ${voucher.workflow_state}`);
    console.log(`Is Resubmitted: ${voucher.is_resubmitted}`);
    console.log(`Rejected By: ${voucher.rejected_by}`);
    console.log(`Rejection Time: ${voucher.rejection_time}`);

    // Apply the exact server logic
    const isResubmission = (voucher.rejected_by && voucher.rejected_by.trim() !== '') ||
                          (voucher.rejection_time) ||
                          (voucher.status === 'VOUCHER REJECTED') ||
                          (voucher.status === 'VOUCHER RETURNED');

    const targetStatus = isResubmission ? 'RE-SUBMISSION' : 'PENDING RECEIPT';
    const targetWorkflowState = isResubmission ? 'FINANCE_RESUBMISSION' : 'FINANCE_PROCESSING';
    const isResubmittedFlag = isResubmission ? 1 : 0;

    console.log(`\n🎯 CALCULATED VALUES:`);
    console.log(`isResubmission: ${isResubmission}`);
    console.log(`targetStatus: ${targetStatus}`);
    console.log(`targetWorkflowState: ${targetWorkflowState}`);
    console.log(`isResubmittedFlag: ${isResubmittedFlag}`);

    // Test the exact UPDATE query from the server
    console.log(`\n🔧 TESTING MANUAL UPDATE...`);
    
    const updateQuery = `
      UPDATE vouchers 
      SET status = ?, 
          workflow_state = ?, 
          is_resubmitted = ?
      WHERE id = ?
    `;
    
    console.log(`Query: ${updateQuery}`);
    console.log(`Parameters: [${targetStatus}, ${targetWorkflowState}, ${isResubmittedFlag}, ${voucher.id}]`);

    const [updateResult] = await connection.execute(updateQuery, [
      targetStatus, 
      targetWorkflowState, 
      isResubmittedFlag, 
      voucher.id
    ]);

    console.log(`\n✅ UPDATE RESULT:`);
    console.log(`Affected Rows: ${updateResult.affectedRows}`);
    console.log(`Changed Rows: ${updateResult.changedRows}`);
    console.log(`Info: ${updateResult.info}`);

    // Verify the update worked
    const [updatedVouchers] = await connection.execute(`
      SELECT 
        id,
        voucher_id, 
        status, 
        workflow_state, 
        is_resubmitted
      FROM vouchers 
      WHERE id = ?
    `, [voucher.id]);

    if (updatedVouchers.length > 0) {
      const updated = updatedVouchers[0];
      console.log(`\n📊 UPDATED VOUCHER STATE:`);
      console.log(`Status: ${updated.status} (Expected: ${targetStatus}) ${updated.status === targetStatus ? '✅' : '❌'}`);
      console.log(`Workflow State: ${updated.workflow_state} (Expected: ${targetWorkflowState}) ${updated.workflow_state === targetWorkflowState ? '✅' : '❌'}`);
      console.log(`Is Resubmitted: ${updated.is_resubmitted} (Expected: ${isResubmittedFlag}) ${updated.is_resubmitted === isResubmittedFlag ? '✅' : '❌'}`);

      if (updated.status === targetStatus && updated.workflow_state === targetWorkflowState && updated.is_resubmitted === isResubmittedFlag) {
        console.log(`\n🎉 MANUAL UPDATE SUCCESSFUL!`);
        console.log(`This proves the database and query work correctly.`);
        console.log(`The issue must be in the server code execution or transaction handling.`);
      } else {
        console.log(`\n❌ MANUAL UPDATE FAILED!`);
        console.log(`There might be a database constraint or trigger preventing the update.`);
      }
    }

  } catch (error) {
    console.error('❌ Error testing manual update:', error);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n🔌 Database connection closed');
    }
  }
}

// Run the test
testManualUpdate();
