const mysql = require('mysql2/promise');
require('dotenv').config();

async function testResubmissionWorkflow() {
  let connection;
  
  try {
    // Create connection
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || 'vms@2025@1989',
      database: process.env.DB_NAME || 'vms_production'
    });

    console.log('🔗 Connected to database');
    console.log('🧪 Testing Complete Re-submission Workflow...\n');

    // Clean up any existing test data first
    console.log('🧹 Cleaning up any existing test data...');
    await connection.execute('DELETE FROM vouchers WHERE id = ?', ['test-resubmit-voucher-001']);
    console.log('✅ Cleanup completed');

    // Step 1: Create a test voucher
    console.log('📝 Step 1: Creating test voucher...');
    const voucherId = 'TEST-RESUBMIT-001';
    const testVoucher = {
      id: 'test-resubmit-voucher-001',
      voucher_id: voucherId,
      date: '2025-01-20',
      claimant: 'John Doe',
      description: 'Test resubmission workflow',
      amount: 1000.00,
      currency: 'GHS',
      department: 'FINANCE',
      original_department: 'FINANCE',
      dispatched_by: 'Felix Ayisi',
      dispatch_time: new Date().toISOString(),
      status: 'PENDING SUBMISSION',
      sent_to_audit: false,
      created_by: 'Felix Ayisi',
      workflow_state: 'FINANCE_PENDING'
    };

    await connection.execute(`
      INSERT INTO vouchers (
        id, voucher_id, date, claimant, description, amount, currency, 
        department, original_department, dispatched_by, dispatch_time, 
        status, sent_to_audit, created_by, workflow_state
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      testVoucher.id, testVoucher.voucher_id, testVoucher.date, testVoucher.claimant,
      testVoucher.description, testVoucher.amount, testVoucher.currency,
      testVoucher.department, testVoucher.original_department, testVoucher.dispatched_by,
      testVoucher.dispatch_time, testVoucher.status, testVoucher.sent_to_audit,
      testVoucher.created_by, testVoucher.workflow_state
    ]);
    console.log('✅ Test voucher created:', voucherId);

    // Step 2: Send to Audit (normal flow)
    console.log('\n📤 Step 2: Sending voucher to Audit...');
    await connection.execute(`
      UPDATE vouchers
      SET status = 'PENDING RECEIPT',
          workflow_state = 'FINANCE_PROCESSING',
          sent_to_audit = true
      WHERE id = ?
    `, [testVoucher.id]);
    console.log('✅ Voucher sent to Audit');

    // Step 3: Audit receives and rejects voucher
    console.log('\n❌ Step 3: Audit rejects voucher...');
    await connection.execute(`
      UPDATE vouchers 
      SET status = 'VOUCHER REJECTED',
          workflow_state = 'FINANCE_REJECTED',
          rejected_by = 'Samuel Asiedu',
          rejection_time = NOW(),
          comment = 'Missing supporting documents - please provide receipts'
      WHERE id = ?
    `, [testVoucher.id]);
    console.log('✅ Voucher rejected by Audit');

    // Step 4: Finance adds voucher back (Add Back button)
    console.log('\n🔄 Step 4: Finance adds voucher back to pending...');
    await connection.execute(`
      UPDATE vouchers 
      SET status = 'PENDING SUBMISSION',
          workflow_state = 'FINANCE_PENDING',
          sent_to_audit = false
      WHERE id = ?
    `, [testVoucher.id]);
    console.log('✅ Voucher added back to pending');

    // Step 5: Finance sends to Audit again (RESUBMISSION)
    console.log('\n🔁 Step 5: Finance resubmits voucher to Audit...');
    await connection.execute(`
      UPDATE vouchers
      SET status = 'RE-SUBMISSION',
          workflow_state = 'FINANCE_RESUBMISSION',
          sent_to_audit = true,
          is_resubmission = true
      WHERE id = ?
    `, [testVoucher.id]);
    console.log('✅ Voucher resubmitted with RE-SUBMISSION status');

    // Step 6: Verify the resubmission workflow
    console.log('\n🔍 Step 6: Verifying resubmission workflow...');
    const [results] = await connection.execute(`
      SELECT voucher_id, status, workflow_state, is_resubmission, comment, rejected_by
      FROM vouchers 
      WHERE id = ?
    `, [testVoucher.id]);

    const voucher = results[0];
    console.log('📊 Final voucher state:');
    console.log(`   Voucher ID: ${voucher.voucher_id}`);
    console.log(`   Status: ${voucher.status}`);
    console.log(`   Workflow State: ${voucher.workflow_state}`);
    console.log(`   Is Resubmission: ${voucher.is_resubmission ? 'YES' : 'NO'}`);
    console.log(`   Original Rejection: ${voucher.comment}`);
    console.log(`   Rejected By: ${voucher.rejected_by}`);

    // Verify expected results
    const isCorrect = voucher.status === 'RE-SUBMISSION' && 
                     voucher.workflow_state === 'FINANCE_RESUBMISSION' &&
                     voucher.is_resubmission === 1;

    if (isCorrect) {
      console.log('\n🎉 ✅ RESUBMISSION WORKFLOW TEST PASSED!');
      console.log('   - Status correctly set to RE-SUBMISSION');
      console.log('   - Workflow state correctly set to FINANCE_RESUBMISSION');
      console.log('   - is_resubmission flag correctly set to true');
      console.log('   - Original rejection reason preserved');
    } else {
      console.log('\n❌ RESUBMISSION WORKFLOW TEST FAILED!');
      console.log('   Expected: status=RE-SUBMISSION, workflow_state=FINANCE_RESUBMISSION, is_resubmission=1');
      console.log(`   Actual: status=${voucher.status}, workflow_state=${voucher.workflow_state}, is_resubmission=${voucher.is_resubmission}`);
    }

    // Clean up test data
    console.log('\n🧹 Cleaning up test data...');
    await connection.execute('DELETE FROM vouchers WHERE id = ?', [testVoucher.id]);
    console.log('✅ Test data cleaned up');

    console.log('\n🎯 RESUBMISSION WORKFLOW TESTING COMPLETE!');

  } catch (error) {
    console.error('❌ Error during resubmission workflow test:', error);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 Database connection closed');
    }
  }
}

// Run the test
testResubmissionWorkflow();
