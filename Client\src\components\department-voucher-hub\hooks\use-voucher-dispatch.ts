import { useState } from 'react';
import { Department, Voucher, TransactionStatus } from '@/lib/types';
import { useAppStore } from '@/lib/store';
import { formatCurrentDate } from '@/lib/store/utils';
import { toast } from 'sonner';

export const useVoucherDispatch = (department: Department) => {
  const updateVoucher = useAppStore((state) => state.updateVoucher);
  const sendVouchersFromAuditToDepartment = useAppStore((state) => state.sendVouchersFromAuditToDepartment);
  const vouchers = useAppStore((state) => state.vouchers);

  const [showSendDialog, setShowSendDialog] = useState(false);
  const [isSending, setIsSending] = useState(false);
  const [dispatchedBy, setDispatchedBy] = useState<string>('NO_SELECTION');
  const [customDispatchName, setCustomDispatchName] = useState<string>('');
  const [selectedVoucherIds, setSelectedVoucherIds] = useState<string[]>([]);

  // Just update the selected person without modifying vouchers
  const handleDispatchSelection = (value: string) => {
    setDispatchedBy(value);
  };

  // Just update the custom name without modifying vouchers
  const handleCustomNameChange = (value: string) => {
    const upperValue = value.toUpperCase();
    setCustomDispatchName(upperValue);
  };

  const handleSendToDepartment = (selectedVouchers: string[], pendingDispatchVouchers: string[]) => {
    if (selectedVouchers.length === 0 && pendingDispatchVouchers.length === 0) {
      toast.error('NO VOUCHERS AVAILABLE TO SEND', {
        duration: 3000,
      });
      return;
    }

    if ((dispatchedBy === 'NO_SELECTION' || !dispatchedBy) && !customDispatchName) {
      toast.error('PLEASE SELECT WHO IS DISPATCHING THESE VOUCHERS', {
        duration: 3000,
      });
      return;
    }

    setShowSendDialog(true);
  };

  const handleConfirmSend = (selectedVouchers: string[], pendingDispatchVouchers: string[]) => {
    if (selectedVouchers.length === 0 && pendingDispatchVouchers.length === 0) return null;

    const voucherIds = selectedVouchers.length > 0
      ? selectedVouchers
      : pendingDispatchVouchers;

    if (voucherIds.length === 0) return null;

    setIsSending(true);

    try {
      const finalDispatchedBy = (dispatchedBy !== 'NO_SELECTION' && dispatchedBy !== 'OTHER')
        ? dispatchedBy
        : customDispatchName.toUpperCase();

      const vouchersToSend = vouchers.filter(v => voucherIds.includes(v.id));

      vouchersToSend.forEach(voucher => {
        // Check if this is a returned voucher
        const isReturnedVoucher = voucher.pendingReturn || false;
        // Check if this is a rejected voucher
        const isRejectedVoucher = voucher.isRejectedVoucher || voucher.status === "REJECTED: PENDING DISPATCH";

        console.log(`Dispatching voucher ${voucher.voucherId} (${voucher.id}), pendingReturn: ${voucher.pendingReturn}, isReturned: ${voucher.isReturned}, isRejectedVoucher: ${isRejectedVoucher}`);

        let updateData: Partial<Voucher>;

        // NEW FLOW: Handle rejected vouchers specially
        if (isRejectedVoucher) {
          console.log(`Dispatching REJECTED voucher ${voucher.id}: will go directly to REJECTED tab, not DISPATCHED tab`);

          updateData = {
            status: "VOUCHER REJECTED", // Keep status as VOUCHER REJECTED
            rejectedDispatchedBy: finalDispatchedBy,
            rejectedDispatchTime: formatCurrentDate(),
            dispatched: false, // CRITICAL: Don't mark as dispatched to avoid appearing in DISPATCHED tab
            // AUDIT TRAIL FIX: Keep audit dispatch fields for permanent record
            auditDispatchedBy: finalDispatchedBy, // Keep for audit trail
            auditDispatchTime: formatCurrentDate(), // Keep for audit trail
            isRejectedVoucher: true,
            comment: voucher.comment // Preserve rejection comment
          };
        }
        // For returned vouchers, preserve both returnComment and comment
        else if (isReturnedVoucher) {
          // First try to use returnComment, then fall back to comment
          const existingReturnComment = voucher.returnComment || voucher.comment;
          const existingComment = voucher.comment || voucher.returnComment;

          // Ensure we have a valid comment
          const finalComment = String(existingReturnComment || existingComment || "NO COMMENT PROVIDED").trim();

          console.log(`Preserving return comment for voucher ${voucher.id}: "${finalComment}"`);

          updateData = {
            dispatched: true,
            status: "VOUCHER RETURNED", // Always use VOUCHER RETURNED for returned vouchers
            dispatchedBy: finalDispatchedBy,
            dispatchTime: formatCurrentDate(),
            isReturned: true,         // Set isReturned to true
            returnComment: finalComment,
            comment: finalComment,
            pendingReturn: false       // Clear pendingReturn flag
          };
        }
        // For non-returned vouchers, dispatch to Finance for batch receiving
        else {
          updateData = {
            dispatched: true,
            status: "AUDIT: PROCESSING", // Keep as AUDIT: PROCESSING - Finance will certify after receiving
            auditDispatchedBy: finalDispatchedBy, // Use audit-specific dispatch fields
            auditDispatchTime: formatCurrentDate(),
            isReturned: false,
            pendingReturn: false,      // Ensure pendingReturn is false
            comment: voucher.comment    // Preserve existing comment
          };
        }

        console.log(`Updating voucher ${voucher.id} with dispatch info:`, {
          dispatchedBy: finalDispatchedBy,
          dispatchTime: formatCurrentDate(),
          isReturned: updateData.isReturned,
          pendingReturn: updateData.pendingReturn,
          status: updateData.status
        });

        updateVoucher(voucher.id, updateData);

        // Verify the update was applied
        setTimeout(() => {
          const updatedVoucher = vouchers.find(v => v.id === voucher.id);
          if (updatedVoucher) {
            console.log(`After dispatch, voucher ${voucher.id} state:`, {
              isReturned: updatedVoucher.isReturned,
              pendingReturn: updatedVoucher.pendingReturn,
              status: updatedVoucher.status,
              dispatched: updatedVoucher.dispatched
            });
          }
        }, 100);
      });

      // Send vouchers to department - this will trigger the receipt process
      sendVouchersFromAuditToDepartment(department, voucherIds);

      toast.success(`${vouchersToSend.length} VOUCHER(S) SENT TO ${department} DEPARTMENT`, {
        duration: 3000,
      });

      setShowSendDialog(false);
      setDispatchedBy('NO_SELECTION');
      setCustomDispatchName('');

      return []; // Return empty array to clear selection
    } catch (error) {
      toast.error('FAILED TO SEND VOUCHERS', {
        duration: 3000,
      });
    } finally {
      setIsSending(false);
    }

    return null; // Return null if no changes to selection
  };

  return {
    showSendDialog,
    setShowSendDialog,
    isSending,
    dispatchedBy,
    setDispatchedBy: handleDispatchSelection,
    customDispatchName,
    setCustomDispatchName: handleCustomNameChange,
    handleSendToDepartment,
    handleConfirmSend,
    selectedVoucherIds,
    setSelectedVoucherIds
  };
};
