-- =====================================================
-- CLEAR ALL VOUCHERS - FRESH START SCRIPT
-- =====================================================
-- This script will completely clear all voucher data
-- from the VMS system for a fresh start
-- =====================================================

-- Disable foreign key checks temporarily for easier deletion
SET FOREIGN_KEY_CHECKS = 0;

-- 1. Clear junction table first (batch_vouchers)
DELETE FROM batch_vouchers;
ALTER TABLE batch_vouchers AUTO_INCREMENT = 1;

-- 2. Clear voucher logs
DELETE FROM voucher_logs;
ALTER TABLE voucher_logs AUTO_INCREMENT = 1;

-- 3. Clear provisional cash records
DELETE FROM provisional_cash_records;
ALTER TABLE provisional_cash_records AUTO_INCREMENT = 1;

-- 4. Clear workflow audit log (if exists)
DELETE FROM workflow_audit_log WHERE 1=1;

-- 5. Clear main vouchers table
DELETE FROM vouchers;
ALTER TABLE vouchers AUTO_INCREMENT = 1;

-- 6. Clear voucher batches
DELETE FROM voucher_batches;
ALTER TABLE voucher_batches AUTO_INCREMENT = 1;

-- 7. Clear voucher-related audit logs
DELETE FROM audit_logs WHERE resource_type IN ('voucher', 'batch', 'voucher_batch');

-- 8. Clear login audit (optional - uncomment if you want to clear login history too)
-- DELETE FROM login_audit;

-- Re-enable foreign key checks
SET FOREIGN_KEY_CHECKS = 1;

-- =====================================================
-- VERIFICATION QUERIES
-- =====================================================
-- Run these to verify all voucher data has been cleared

SELECT 'vouchers' as table_name, COUNT(*) as record_count FROM vouchers
UNION ALL
SELECT 'voucher_batches' as table_name, COUNT(*) as record_count FROM voucher_batches
UNION ALL
SELECT 'batch_vouchers' as table_name, COUNT(*) as record_count FROM batch_vouchers
UNION ALL
SELECT 'voucher_logs' as table_name, COUNT(*) as record_count FROM voucher_logs
UNION ALL
SELECT 'provisional_cash_records' as table_name, COUNT(*) as record_count FROM provisional_cash_records
UNION ALL
SELECT 'audit_logs (voucher-related)' as table_name, COUNT(*) as record_count FROM audit_logs WHERE resource_type IN ('voucher', 'batch', 'voucher_batch');

-- =====================================================
-- SUCCESS MESSAGE
-- =====================================================
SELECT 'SUCCESS: All voucher data has been cleared! System is ready for fresh start.' as status;
