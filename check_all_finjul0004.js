const mysql = require('mysql2/promise');

async function checkAllFINJUL0004() {
  const connection = await mysql.createConnection({
    host: 'localhost',
    user: 'root',
    password: 'vms@2025@1989',
    database: 'vms_production'
  });
  
  try {
    const [vouchers] = await connection.execute(`
      SELECT voucher_id, status, workflow_state, department, is_resubmitted, certified_by, rejected_by
      FROM vouchers 
      WHERE voucher_id LIKE 'FINJUL0004%' 
      ORDER BY voucher_id
    `);
    
    console.log('🔍 ALL FINJUL0004 VOUCHERS:');
    console.log('===========================');
    
    vouchers.forEach(v => {
      console.log(`📋 ${v.voucher_id}:`);
      console.log(`   Status: ${v.status}`);
      console.log(`   Workflow: ${v.workflow_state}`);
      console.log(`   Department: ${v.department}`);
      console.log(`   Resubmitted: ${v.is_resubmitted}`);
      console.log(`   Certified: ${v.certified_by || 'NO'}`);
      console.log(`   Rejected: ${v.rejected_by || 'NO'}`);
      console.log('');
    });
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await connection.end();
  }
}

checkAllFINJUL0004().catch(console.error);
