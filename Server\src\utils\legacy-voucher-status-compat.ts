/**
 * Legacy Voucher Status Compatibility Layer
 * Temporary compatibility layer for legacy code during migration
 * TODO: Remove this file once all legacy code is migrated to workflow state machine
 */

import { WorkflowState } from '../workflow/VoucherWorkflowStateMachine';

// Legacy status constants for backward compatibility
export const VOUCHER_STATUSES = {
  PENDING: 'PENDING SUBMISSION' as const,
  PENDING_RECEIPT: 'PENDING RECEIPT' as const,
  VOUCHER_PROCESSING: 'VOUCHER PROCESSING' as const,
  AUDIT_PROCESSING: 'AUDIT: PROCESSING' as const,
  VOUCHER_CERTIFIED: 'VOUCHER CERTIFIED' as const,
  VOUCHER_REJECTED: 'VOUCHER REJECTED' as const,
  VOUCHER_RETURNED: 'VOUCHER RETURNED' as const,
  PENDING_DISPATCH: 'PENDING DISPATCH' as const,
  DISPATCHED: 'DISPATCHED' as const
};

// Legacy flag synchronization (simplified)
export function synchronizeVoucherFlags(options: { status: string }) {
  const { status } = options;
  
  // Map legacy status to workflow flags
  const flags = {
    isNew: status === VOUCHER_STATUSES.PENDING,
    isPendingDispatch: status === VOUCHER_STATUSES.PENDING_DISPATCH,
    isDispatched: status === VOUCHER_STATUSES.DISPATCHED,
    isCertified: status === VOUCHER_STATUSES.VOUCHER_CERTIFIED,
    isRejected: status === VOUCHER_STATUSES.VOUCHER_REJECTED,
    isReturned: status === VOUCHER_STATUSES.VOUCHER_RETURNED,
    isProcessing: status === VOUCHER_STATUSES.VOUCHER_PROCESSING || status === VOUCHER_STATUSES.AUDIT_PROCESSING
  };

  return { flags };
}

// Legacy status transition validation (simplified)
export function isValidStatusTransition(
  fromStatus: string, 
  toStatus: string, 
  userRole?: string, 
  voucher?: any
): { isValid: boolean; reason?: string } {
  
  // For now, allow all transitions during migration
  // TODO: Implement proper validation using workflow state machine
  console.warn('⚠️  Using legacy status transition validation - migrate to workflow state machine');
  
  // Basic validation rules
  const validTransitions: Record<string, string[]> = {
    [VOUCHER_STATUSES.PENDING]: [VOUCHER_STATUSES.PENDING_RECEIPT],
    [VOUCHER_STATUSES.PENDING_RECEIPT]: [VOUCHER_STATUSES.VOUCHER_PROCESSING, VOUCHER_STATUSES.VOUCHER_RETURNED],
    [VOUCHER_STATUSES.VOUCHER_PROCESSING]: [VOUCHER_STATUSES.VOUCHER_CERTIFIED, VOUCHER_STATUSES.VOUCHER_REJECTED, VOUCHER_STATUSES.VOUCHER_RETURNED],
    [VOUCHER_STATUSES.AUDIT_PROCESSING]: [VOUCHER_STATUSES.PENDING_DISPATCH],
    [VOUCHER_STATUSES.PENDING_DISPATCH]: [VOUCHER_STATUSES.DISPATCHED],
    [VOUCHER_STATUSES.VOUCHER_REJECTED]: [VOUCHER_STATUSES.VOUCHER_PROCESSING],
    [VOUCHER_STATUSES.VOUCHER_RETURNED]: [VOUCHER_STATUSES.VOUCHER_PROCESSING]
  };

  const allowedTransitions = validTransitions[fromStatus] || [];
  const isValid = allowedTransitions.includes(toStatus);

  return {
    isValid,
    reason: isValid ? undefined : `Transition from ${fromStatus} to ${toStatus} not allowed`
  };
}

// Legacy status transition validation (backward compatibility)
export function isValidStatusTransition_old(
  fromStatus: string, 
  toStatus: string, 
  userRole?: string, 
  voucher?: any
): boolean {
  const result = isValidStatusTransition(fromStatus, toStatus, userRole, voucher);
  return result.isValid;
}

// Map legacy status to workflow state
export function mapLegacyStatusToWorkflowState(legacyStatus: string): WorkflowState {
  const mapping: Record<string, WorkflowState> = {
    [VOUCHER_STATUSES.PENDING]: WorkflowState.FINANCE_PENDING,
    [VOUCHER_STATUSES.PENDING_RECEIPT]: WorkflowState.FINANCE_PROCESSING,
    [VOUCHER_STATUSES.VOUCHER_PROCESSING]: WorkflowState.AUDIT_NEW,
    [VOUCHER_STATUSES.AUDIT_PROCESSING]: WorkflowState.AUDIT_PENDING_DISPATCH,
    [VOUCHER_STATUSES.PENDING_DISPATCH]: WorkflowState.AUDIT_PENDING_DISPATCH,
    [VOUCHER_STATUSES.DISPATCHED]: WorkflowState.AUDIT_DISPATCHED,
    [VOUCHER_STATUSES.VOUCHER_CERTIFIED]: WorkflowState.FINANCE_CERTIFIED,
    [VOUCHER_STATUSES.VOUCHER_REJECTED]: WorkflowState.FINANCE_REJECTED,
    [VOUCHER_STATUSES.VOUCHER_RETURNED]: WorkflowState.FINANCE_RETURNED
  };

  return mapping[legacyStatus] || WorkflowState.FINANCE_PENDING;
}

// Map workflow state to legacy status
export function mapWorkflowStateToLegacyStatus(workflowState: WorkflowState): string {
  const mapping: Record<WorkflowState, string> = {
    [WorkflowState.FINANCE_PENDING]: VOUCHER_STATUSES.PENDING,
    [WorkflowState.FINANCE_PROCESSING]: VOUCHER_STATUSES.PENDING_RECEIPT,
    [WorkflowState.FINANCE_CERTIFIED]: VOUCHER_STATUSES.VOUCHER_CERTIFIED,
    [WorkflowState.FINANCE_REJECTED]: VOUCHER_STATUSES.VOUCHER_REJECTED,
    [WorkflowState.FINANCE_RETURNED]: VOUCHER_STATUSES.VOUCHER_RETURNED,
    [WorkflowState.FINANCE_RESUBMISSION_RECEIVED]: VOUCHER_STATUSES.VOUCHER_CERTIFIED, // Certified resubmissions
    [WorkflowState.AUDIT_NEW]: VOUCHER_STATUSES.VOUCHER_PROCESSING,
    [WorkflowState.AUDIT_NEW_RESUBMITTED]: VOUCHER_STATUSES.VOUCHER_PROCESSING,
    [WorkflowState.AUDIT_PENDING_DISPATCH]: VOUCHER_STATUSES.AUDIT_PROCESSING,
    [WorkflowState.AUDIT_PENDING_DISPATCH_REJECTED]: VOUCHER_STATUSES.AUDIT_PROCESSING,
    [WorkflowState.AUDIT_PENDING_DISPATCH_RETURNED]: VOUCHER_STATUSES.AUDIT_PROCESSING,
    [WorkflowState.AUDIT_DISPATCHED]: VOUCHER_STATUSES.DISPATCHED,
    [WorkflowState.AUDIT_REJECTED_COPY]: VOUCHER_STATUSES.VOUCHER_REJECTED,
    [WorkflowState.AUDIT_RETURNED_COPY]: VOUCHER_STATUSES.VOUCHER_RETURNED
  };

  return mapping[workflowState] || VOUCHER_STATUSES.PENDING;
}

// Migration helper: Update voucher with both legacy status and workflow state
export async function updateVoucherWithBothSystems(
  db: any,
  voucherId: string,
  workflowState: WorkflowState,
  additionalFields: Record<string, any> = {}
) {
  const legacyStatus = mapWorkflowStateToLegacyStatus(workflowState);
  const { flags } = synchronizeVoucherFlags({ status: legacyStatus });

  const updateFields = {
    status: legacyStatus,
    workflow_state: workflowState,
    flags: JSON.stringify(flags),
    last_modified: new Date(),
    ...additionalFields
  };

  const setClause = Object.keys(updateFields)
    .map(key => `${key} = ?`)
    .join(', ');

  const values = [...Object.values(updateFields), voucherId];

  await db.execute(`
    UPDATE vouchers 
    SET ${setClause}
    WHERE id = ?
  `, values);

  console.log(`✅ Updated voucher ${voucherId}: ${legacyStatus} (${workflowState})`);
}

// Deprecation warnings
export function warnLegacyUsage(functionName: string, location: string) {
  console.warn(`⚠️  DEPRECATED: ${functionName} used in ${location}`);
  console.warn(`   Please migrate to workflow state machine`);
  console.warn(`   See: /workflow/VoucherWorkflowStateMachine.ts`);
}
