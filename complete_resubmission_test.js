const mysql = require('mysql2/promise');
const axios = require('axios');

const BASE_URL = 'http://localhost:8080/api';

// Database connection
async function createConnection() {
  return await mysql.createConnection({
    host: 'localhost',
    user: 'root',
    password: 'vms@2025@1989',
    database: 'vms_production'
  });
}

// Test the complete resubmission workflow
async function testCompleteResubmissionWorkflow() {
  let connection;
  
  try {
    connection = await createConnection();
    
    console.log('🚀 COMPLETE RESUBMISSION WORKFLOW TEST');
    console.log('=====================================');
    
    // Step 1: Create a fresh test voucher using API
    console.log('\n📝 STEP 1: Creating fresh test voucher...');

    const testVoucher = {
      claimant: 'TEST RESUBMISSION USER',
      amount: 1500.00,
      description: 'Test voucher for resubmission workflow',
      department: 'FINANCE'
    };

    try {
      const createResponse = await axios.post(`${BASE_URL}/vouchers`, testVoucher);
      const voucherId = createResponse.data.id;
      console.log(`✅ Created voucher via API: ${createResponse.data.voucherId} (ID: ${voucherId})`);

      // Verify voucher was created
      const [createdVoucher] = await connection.execute(`
        SELECT id, voucher_id, status, workflow_state FROM vouchers WHERE id = ?
      `, [voucherId]);

      console.log('📊 Initial voucher state:', createdVoucher[0]);

      // Step 2: Send voucher to Audit (first time)
      console.log('\n📤 STEP 2: Sending voucher to Audit (first time)...');

    try {
      const sendResponse = await axios.post(`${BASE_URL}/vouchers/send-to-audit`, {
        voucherIds: [voucherId],
        dispatchedBy: 'FELIX AYISI'
      });
      
      console.log('✅ Send to Audit response:', sendResponse.status);
      
      // Check voucher status after sending
      const [afterSend] = await connection.execute(`
        SELECT voucher_id, status, workflow_state, batch_id, sent_to_audit, is_resubmitted
        FROM vouchers WHERE id = ?
      `, [voucherId]);
      
      console.log('📊 Voucher after first send:', afterSend[0]);
      
      } catch (error) {
        console.log('❌ Send to Audit failed:', error.response?.data || error.message);
      }

      // Step 3: Simulate Audit rejection
      console.log('\n❌ STEP 3: Simulating Audit rejection...');
    
    await connection.execute(`
      UPDATE vouchers 
      SET status = 'VOUCHER REJECTED',
          workflow_state = 'AUDIT_REJECTED',
          rejected_by = 'SAMUEL ASIEDU',
          rejection_time = NOW(),
          comment = 'Test rejection for resubmission workflow'
      WHERE id = ?
    `, [voucherId]);
    
    console.log('✅ Voucher rejected by Audit');
    
    // Step 4: Add back to pending (simulate frontend action)
    console.log('\n🔄 STEP 4: Adding voucher back to pending...');
    
    await connection.execute(`
      UPDATE vouchers 
      SET status = 'PENDING SUBMISSION',
          workflow_state = 'FINANCE_PENDING',
          sent_to_audit = false,
          batch_id = NULL
      WHERE id = ?
    `, [voucherId]);
    
    // Check voucher state before resubmission
    const [beforeResubmit] = await connection.execute(`
      SELECT voucher_id, status, workflow_state, rejected_by, rejection_time, is_resubmitted
      FROM vouchers WHERE id = ?
    `, [voucherId]);
    
    console.log('📊 Voucher before resubmission:', beforeResubmit[0]);
    console.log('🧪 Resubmission detection should trigger because:');
    console.log(`   - Has rejected_by: ${beforeResubmit[0].rejected_by ? 'YES' : 'NO'}`);
    console.log(`   - Has rejection_time: ${beforeResubmit[0].rejection_time ? 'YES' : 'NO'}`);
    
    // Step 5: Send voucher to Audit again (RESUBMISSION)
    console.log('\n🔄 STEP 5: Sending voucher to Audit AGAIN (should trigger resubmission)...');
    
    try {
      const resubmitResponse = await axios.post(`${BASE_URL}/vouchers/send-to-audit`, {
        voucherIds: [voucherId],
        dispatchedBy: 'FELIX AYISI'
      });
      
      console.log('✅ Resubmission response:', resubmitResponse.status);
      
      // Check voucher status after resubmission
      const [afterResubmit] = await connection.execute(`
        SELECT voucher_id, status, workflow_state, batch_id, sent_to_audit, is_resubmitted,
               rejected_by, rejection_time
        FROM vouchers WHERE id = ?
      `, [voucherId]);
      
      console.log('\n🎯 RESUBMISSION RESULT:');
      console.log('======================');
      const voucher = afterResubmit[0];
      console.log(`Voucher ID: ${voucher.voucher_id}`);
      console.log(`Status: ${voucher.status}`);
      console.log(`Workflow State: ${voucher.workflow_state}`);
      console.log(`Is Resubmitted: ${voucher.is_resubmitted ? 'YES' : 'NO'}`);
      console.log(`Batch ID: ${voucher.batch_id || 'None'}`);
      console.log(`Rejected By: ${voucher.rejected_by || 'None'}`);
      console.log(`Rejection Time: ${voucher.rejection_time || 'None'}`);
      
      // Expected results
      console.log('\n✅ EXPECTED RESULTS:');
      console.log(`Status should be: RE-SUBMISSION (actual: ${voucher.status})`);
      console.log(`Workflow should be: FINANCE_RESUBMISSION (actual: ${voucher.workflow_state})`);
      console.log(`Is Resubmitted should be: YES (actual: ${voucher.is_resubmitted ? 'YES' : 'NO'})`);
      
      // Check if results match expectations
      const statusCorrect = voucher.status === 'RE-SUBMISSION';
      const workflowCorrect = voucher.workflow_state === 'FINANCE_RESUBMISSION';
      const flagCorrect = voucher.is_resubmitted === true;
      
      console.log('\n🎯 TEST RESULTS:');
      console.log(`Status: ${statusCorrect ? '✅ PASS' : '❌ FAIL'}`);
      console.log(`Workflow: ${workflowCorrect ? '✅ PASS' : '❌ FAIL'}`);
      console.log(`Flag: ${flagCorrect ? '✅ PASS' : '❌ FAIL'}`);
      
      if (statusCorrect && workflowCorrect && flagCorrect) {
        console.log('\n🎉 RESUBMISSION WORKFLOW: ✅ SUCCESS!');
        console.log('The resubmission detection is working correctly!');
      } else {
        console.log('\n❌ RESUBMISSION WORKFLOW: FAILED!');
        console.log('The resubmission detection is not working properly.');
      }
      
      // Check batch information
      if (voucher.batch_id) {
        const [batchInfo] = await connection.execute(`
          SELECT id, contains_resubmissions, resubmission_count
          FROM voucher_batches WHERE id = ?
        `, [voucher.batch_id]);
        
        if (batchInfo.length > 0) {
          console.log('\n📦 BATCH INFORMATION:');
          console.log(`Contains Resubmissions: ${batchInfo[0].contains_resubmissions ? 'YES' : 'NO'}`);
          console.log(`Resubmission Count: ${batchInfo[0].resubmission_count || 0}`);
        }
      }
      
    } catch (error) {
      console.log('❌ Resubmission failed:', error.response?.data || error.message);
      console.log('This indicates the server resubmission logic has an issue.');
    }
    
      // Cleanup
      console.log('\n🧹 CLEANUP: Removing test voucher...');
      await connection.execute('DELETE FROM vouchers WHERE id = ?', [voucherId]);
      console.log('✅ Test voucher removed');

    } catch (error) {
      console.log('❌ Voucher creation failed:', error.response?.data || error.message);
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// Run the test
testCompleteResubmissionWorkflow();
