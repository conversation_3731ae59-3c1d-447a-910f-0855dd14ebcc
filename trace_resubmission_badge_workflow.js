/**
 * RESUBMISSION BADGE WORKFLOW TRACER
 * Traces the RESUBMISSION badge through the complete end-to-end workflow
 * to identify duplicate badge sources and validate workflow functionality
 */

const mysql = require('mysql2/promise');

class ResubmissionWorkflowTracer {
  constructor() {
    this.dbConfig = {
      host: 'localhost',
      user: 'root',
      password: 'vms@2025@1989',
      database: 'vms_production'
    };
  }

  async connect() {
    this.connection = await mysql.createConnection(this.dbConfig);
  }

  async disconnect() {
    if (this.connection) {
      await this.connection.end();
    }
  }

  async traceResubmissionWorkflow() {
    console.log('🔍 TRACING RESUBMISSION BADGE WORKFLOW');
    console.log('=====================================');

    // STAGE 1: Find resubmitted vouchers
    console.log('\n📋 STAGE 1: RESUBMITTED VOUCHERS IN DATABASE');
    console.log('--------------------------------------------');
    
    const [resubmittedVouchers] = await this.connection.execute(`
      SELECT 
        voucher_id,
        id,
        status,
        workflow_state,
        department,
        original_department,
        is_resubmitted,
        rejected_by,
        rejection_time,
        batch_id,
        sent_to_audit,
        work_started,
        badge_type,
        dispatched,
        audit_dispatched_by,
        certified_by,
        department_receipt_time
      FROM vouchers 
      WHERE is_resubmitted = 1 
      ORDER BY created_at DESC
    `);

    resubmittedVouchers.forEach((voucher, index) => {
      console.log(`\n${index + 1}. VOUCHER: ${voucher.voucher_id}`);
      console.log(`   ID: ${voucher.id}`);
      console.log(`   Status: ${voucher.status}`);
      console.log(`   Workflow State: ${voucher.workflow_state}`);
      console.log(`   Department: ${voucher.department}`);
      console.log(`   Original Department: ${voucher.original_department}`);
      console.log(`   Is Resubmitted: ${voucher.is_resubmitted ? 'YES' : 'NO'}`);
      console.log(`   Badge Type: ${voucher.badge_type}`);
      console.log(`   Rejected By: ${voucher.rejected_by || 'None'}`);
      console.log(`   Batch ID: ${voucher.batch_id || 'None'}`);
      console.log(`   Sent to Audit: ${voucher.sent_to_audit ? 'YES' : 'NO'}`);
      console.log(`   Work Started: ${voucher.work_started ? 'YES' : 'NO'}`);
      console.log(`   Dispatched: ${voucher.dispatched ? 'YES' : 'NO'}`);
      console.log(`   Audit Dispatched By: ${voucher.audit_dispatched_by || 'None'}`);
      console.log(`   Certified By: ${voucher.certified_by || 'None'}`);
      console.log(`   Dept Receipt Time: ${voucher.department_receipt_time || 'None'}`);
      
      // Determine current workflow stage
      let currentStage = 'UNKNOWN';
      if (voucher.certified_by) {
        currentStage = '7. CERTIFIED (Finance)';
      } else if (voucher.department_receipt_time && voucher.dispatched) {
        currentStage = '6. RECEIVED BY FINANCE';
      } else if (voucher.dispatched && voucher.audit_dispatched_by) {
        currentStage = '5. DISPATCHED (Audit → Finance)';
      } else if (voucher.work_started && voucher.department === 'AUDIT') {
        currentStage = '4. PENDING DISPATCH (Audit)';
      } else if (voucher.department === 'AUDIT' && voucher.sent_to_audit) {
        currentStage = '3. NEW VOUCHERS (Audit)';
      } else if (voucher.batch_id && !voucher.department_receipt_time) {
        currentStage = '2. BATCH RECEIVING (Audit)';
      } else {
        currentStage = '1. DISPATCHED (Finance → Audit)';
      }
      
      console.log(`   🎯 CURRENT STAGE: ${currentStage}`);
      
      // Badge analysis
      console.log(`\n   🏷️ BADGE ANALYSIS:`);
      console.log(`   - is_resubmitted: ${voucher.is_resubmitted ? 'YES' : 'NO'}`);
      console.log(`   - status === 'RE-SUBMISSION': ${voucher.status === 'RE-SUBMISSION' ? 'YES' : 'NO'}`);
      console.log(`   - workflow_state === 'AUDIT_NEW_RESUBMITTED': ${voucher.workflow_state === 'AUDIT_NEW_RESUBMITTED' ? 'YES' : 'NO'}`);
      console.log(`   - rejected_by exists: ${voucher.rejected_by ? 'YES' : 'NO'}`);
      console.log(`   - badge_type: ${voucher.badge_type}`);
      
      const shouldShowBadge = voucher.is_resubmitted || 
                             voucher.status === 'RE-SUBMISSION' ||
                             voucher.workflow_state === 'AUDIT_NEW_RESUBMITTED' ||
                             (voucher.rejected_by && voucher.rejected_by.trim() !== '');
      
      console.log(`   ✅ SHOULD SHOW RESUBMISSION BADGE: ${shouldShowBadge ? 'YES' : 'NO'}`);
    });

    // STAGE 2: Check batches containing resubmitted vouchers
    console.log('\n\n📦 STAGE 2: BATCHES WITH RESUBMITTED VOUCHERS');
    console.log('---------------------------------------------');
    
    const [batches] = await this.connection.execute(`
      SELECT DISTINCT
        b.id as batch_id,
        b.department,
        b.contains_resubmissions,
        b.received,
        b.created_at,
        COUNT(v.id) as voucher_count,
        SUM(v.is_resubmitted) as resubmitted_count
      FROM voucher_batches b
      LEFT JOIN vouchers v ON b.id = v.batch_id
      WHERE b.contains_resubmissions = 1
      GROUP BY b.id
      ORDER BY b.created_at DESC
    `);

    batches.forEach((batch, index) => {
      console.log(`\n${index + 1}. BATCH: ${batch.batch_id}`);
      console.log(`   Department: ${batch.department}`);
      console.log(`   Contains Resubmissions: ${batch.contains_resubmissions ? 'YES' : 'NO'}`);
      console.log(`   Received: ${batch.received ? 'YES' : 'NO'}`);
      console.log(`   Total Vouchers: ${batch.voucher_count}`);
      console.log(`   Resubmitted Vouchers: ${batch.resubmitted_count}`);
      console.log(`   Created: ${batch.created_at}`);
    });

    return resubmittedVouchers;
  }

  async analyzeComponentBadgeLogic() {
    console.log('\n\n🔧 STAGE 3: COMPONENT BADGE LOGIC ANALYSIS');
    console.log('==========================================');
    
    console.log('\n📋 COMPONENTS THAT SHOULD SHOW RESUBMISSION BADGE:');
    console.log('1. VoucherBatchReceiving.tsx - Batch receiving window');
    console.log('2. NewVouchersTab.tsx - NEW VOUCHERS tab (uses VoucherBadges)');
    console.log('3. VoucherTableBody.tsx - PROCESSING tab (direct badge logic)');
    console.log('4. PendingDispatchTab.tsx - PENDING DISPATCH tab (direct badge logic)');
    console.log('5. DispatchedVouchersTab.tsx - DISPATCHED tab');
    console.log('6. VoucherTable.tsx - CERTIFIED/other tabs');
    
    console.log('\n⚠️ POTENTIAL DUPLICATE SOURCES:');
    console.log('- VoucherBadges component + VoucherTableBody direct logic');
    console.log('- Multiple badge systems in same component');
    console.log('- Badge logic in both parent and child components');
  }
}

async function main() {
  const tracer = new ResubmissionWorkflowTracer();
  
  try {
    await tracer.connect();
    const resubmittedVouchers = await tracer.traceResubmissionWorkflow();
    await tracer.analyzeComponentBadgeLogic();
    
    console.log('\n\n🎯 WORKFLOW VALIDATION SUMMARY');
    console.log('==============================');
    console.log(`Found ${resubmittedVouchers.length} resubmitted voucher(s)`);
    
    if (resubmittedVouchers.length > 0) {
      console.log('\n📋 NEXT STEPS FOR TESTING:');
      console.log('1. Check each voucher in the UI at its current stage');
      console.log('2. Verify RESUBMISSION badge appears (should be 1, not 2)');
      console.log('3. Progress voucher through workflow stages');
      console.log('4. Confirm badge persists at each stage');
    }
    
  } catch (error) {
    console.error('❌ Error tracing workflow:', error);
  } finally {
    await tracer.disconnect();
  }
}

main().catch(console.error);
