const mysql = require('mysql2/promise');
require('dotenv').config();

async function testResubmissionDetection() {
  let connection;
  
  try {
    // Create connection
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || 'vms@2025@1989',
      database: process.env.DB_NAME || 'vms_production'
    });

    console.log('🔗 Connected to database');
    console.log('🧪 Testing resubmission detection on KAY voucher...\n');

    // Get the KAY voucher that was resubmitted
    const [vouchers] = await connection.execute(`
      SELECT 
        id,
        voucher_id, 
        claimant, 
        status, 
        workflow_state, 
        is_resubmitted, 
        sent_to_audit,
        batch_id,
        rejected_by,
        rejection_time,
        comment
      FROM vouchers 
      WHERE claimant = 'KAY' AND voucher_id = 'FINJUL0001'
    `);

    if (vouchers.length === 0) {
      console.log('❌ No KAY voucher found');
      return;
    }

    const voucher = vouchers[0];
    console.log('📊 KAY Voucher Current State:');
    console.log(`ID: ${voucher.id}`);
    console.log(`Voucher ID: ${voucher.voucher_id}`);
    console.log(`Status: ${voucher.status}`);
    console.log(`Workflow State: ${voucher.workflow_state}`);
    console.log(`Is Resubmitted: ${voucher.is_resubmitted ? 'YES' : 'NO'}`);
    console.log(`Sent to Audit: ${voucher.sent_to_audit ? 'YES' : 'NO'}`);
    console.log(`Batch ID: ${voucher.batch_id}`);
    console.log(`Rejected By: ${voucher.rejected_by || 'None'}`);
    console.log(`Rejection Time: ${voucher.rejection_time || 'None'}`);
    console.log(`Comment: ${voucher.comment || 'None'}`);

    // Test the exact resubmission detection logic from the server
    console.log('\n🧪 Testing Server Resubmission Detection Logic:');
    
    const condition1 = voucher.rejected_by && voucher.rejected_by.trim() !== '';
    const condition2 = voucher.rejection_time;
    const condition3 = voucher.status === 'VOUCHER REJECTED';
    const condition4 = voucher.status === 'VOUCHER RETURNED';
    
    console.log(`Condition 1 (has rejected_by): ${condition1} (${voucher.rejected_by})`);
    console.log(`Condition 2 (has rejection_time): ${condition2} (${voucher.rejection_time})`);
    console.log(`Condition 3 (status = VOUCHER REJECTED): ${condition3}`);
    console.log(`Condition 4 (status = VOUCHER RETURNED): ${condition4}`);
    
    const isResubmission = condition1 || condition2 || condition3 || condition4;
    console.log(`\n🎯 DETECTION RESULT: isResubmission = ${isResubmission}`);
    
    // What should have happened
    const expectedStatus = isResubmission ? 'RE-SUBMISSION' : 'PENDING RECEIPT';
    const expectedWorkflowState = isResubmission ? 'FINANCE_RESUBMISSION' : 'FINANCE_PROCESSING';
    const expectedIsResubmitted = isResubmission ? 1 : 0;
    
    console.log('\n📋 Expected vs Actual:');
    console.log(`Status: Expected="${expectedStatus}", Actual="${voucher.status}" ${voucher.status === expectedStatus ? '✅' : '❌'}`);
    console.log(`Workflow State: Expected="${expectedWorkflowState}", Actual="${voucher.workflow_state}" ${voucher.workflow_state === expectedWorkflowState ? '✅' : '❌'}`);
    console.log(`Is Resubmitted: Expected=${expectedIsResubmitted}, Actual=${voucher.is_resubmitted} ${voucher.is_resubmitted === expectedIsResubmitted ? '✅' : '❌'}`);

    // Check the batch
    if (voucher.batch_id) {
      console.log('\n📦 Checking Batch:');
      const [batches] = await connection.execute(`
        SELECT 
          id,
          contains_resubmissions,
          resubmission_count
        FROM voucher_batches 
        WHERE id = ?
      `, [voucher.batch_id]);

      if (batches.length > 0) {
        const batch = batches[0];
        const expectedContainsResubmissions = isResubmission ? 1 : 0;
        const expectedResubmissionCount = isResubmission ? 1 : 0;
        
        console.log(`Contains Resubmissions: Expected=${expectedContainsResubmissions}, Actual=${batch.contains_resubmissions} ${batch.contains_resubmissions === expectedContainsResubmissions ? '✅' : '❌'}`);
        console.log(`Resubmission Count: Expected=${expectedResubmissionCount}, Actual=${batch.resubmission_count || 0} ${(batch.resubmission_count || 0) === expectedResubmissionCount ? '✅' : '❌'}`);
      }
    }

    console.log('\n🔧 DIAGNOSIS:');
    if (isResubmission) {
      if (voucher.status !== 'RE-SUBMISSION') {
        console.log('❌ RESUBMISSION DETECTION FAILED IN SERVER');
        console.log('   The logic should detect this as a resubmission but server did not apply it');
        console.log('   Possible causes:');
        console.log('   1. Server code not compiled with latest changes');
        console.log('   2. Server not restarted after compilation');
        console.log('   3. Different code path being executed');
        console.log('   4. Error in server logic preventing update');
      } else {
        console.log('✅ RESUBMISSION DETECTION WORKING');
      }
    } else {
      console.log('❌ RESUBMISSION DETECTION LOGIC ERROR');
      console.log('   This voucher should be detected as a resubmission but logic says NO');
    }

  } catch (error) {
    console.error('❌ Error testing resubmission detection:', error);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n🔌 Database connection closed');
    }
  }
}

// Run the test
testResubmissionDetection();
