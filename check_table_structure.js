const mysql = require('mysql2/promise');

async function checkTable() {
  let connection;
  try {
    connection = await mysql.createConnection({
      host: 'localhost', 
      user: 'root', 
      password: 'vms@2025@1989', 
      database: 'vms_production'
    });
    
    console.log('📋 VOUCHER_BATCHES TABLE STRUCTURE:');
    console.log('==================================');
    
    const [columns] = await connection.execute('DESCRIBE voucher_batches');
    columns.forEach(col => {
      console.log(`  ${col.Field} - ${col.Type}`);
    });
    
    console.log('\n📦 CURRENT BATCHES:');
    console.log('==================');
    
    const [batches] = await connection.execute('SELECT * FROM voucher_batches ORDER BY id DESC');
    
    if (batches.length === 0) {
      console.log('❌ No batches found');
    } else {
      batches.forEach((batch, index) => {
        console.log(`\n${index + 1}. Batch ID: ${batch.id}`);
        console.log(`   Department: ${batch.department}`);
        console.log(`   Sent By: ${batch.sent_by}`);
        console.log(`   Sent Time: ${batch.sent_time}`);
        console.log(`   Received: ${batch.received ? 'YES' : 'NO'}`);
        console.log(`   Contains Resubmissions: ${batch.contains_resubmissions ? 'YES' : 'NO'}`);
        console.log(`   Resubmission Count: ${batch.resubmission_count || 0}`);
      });
    }
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

checkTable();
