{"version": 3, "file": "VoucherWorkflowStateMachine.js", "sourceRoot": "", "sources": ["../../src/workflow/VoucherWorkflowStateMachine.ts"], "names": [], "mappings": ";AAAA;;;;GAIG;;;AAEH,IAAY,aAkBX;AAlBD,WAAY,aAAa;IACvB,iBAAiB;IACjB,oDAAmC,CAAA;IACnC,0DAAyC,CAAA;IACzC,wDAAuC,CAAA;IACvC,sDAAqC,CAAA;IACrC,sDAAqC,CAAA;IACrC,gFAA+D,CAAA;IAE/D,eAAe;IACf,wCAAuB,CAAA;IACvB,gEAA+C,CAAA;IAC/C,kEAAiD,CAAA;IACjD,oFAAmE,CAAA;IACnE,oFAAmE,CAAA;IACnE,sDAAqC,CAAA;IACrC,4DAA2C,CAAA;IAC3C,4DAA2C,CAAA;AAC7C,CAAC,EAlBW,aAAa,6BAAb,aAAa,QAkBxB;AAED,IAAY,aAcX;AAdD,WAAY,aAAa;IACvB,iBAAiB;IACjB,kDAAiC,CAAA;IACjC,oDAAmC,CAAA;IACnC,kEAAiD,CAAA;IACjD,kEAAiD,CAAA;IAEjD,eAAe;IACf,8DAA6C,CAAA;IAC7C,0CAAyB,CAAA;IACzB,oDAAmC,CAAA;IACnC,kDAAiC,CAAA;IACjC,kDAAiC,CAAA;IACjC,4DAA2C,CAAA;AAC7C,CAAC,EAdW,aAAa,6BAAb,aAAa,QAcxB;AAED,IAAY,SAMX;AAND,WAAY,SAAS;IACnB,0BAAa,CAAA;IACb,0CAA6B,CAAA;IAC7B,0CAA6B,CAAA;IAC7B,kCAAqB,CAAA;IACrB,kCAAqB,CAAA;AACvB,CAAC,EANW,SAAS,yBAAT,SAAS,QAMpB;AAqBD,MAAa,2BAA2B;IAC9B,MAAM,CAAU,WAAW,GAAyB;QAC1D,cAAc;QACd;YACE,SAAS,EAAE,aAAa,CAAC,eAAe;YACxC,KAAK,EAAE,aAAa,CAAC,eAAe;YACpC,OAAO,EAAE,aAAa,CAAC,kBAAkB;YACzC,OAAO,EAAE,IAAI;SACd;QACD;YACE,SAAS,EAAE,aAAa,CAAC,kBAAkB;YAC3C,KAAK,EAAE,aAAa,CAAC,oBAAoB;YACzC,OAAO,EAAE,aAAa,CAAC,SAAS;YAChC,OAAO,EAAE,IAAI;SACd;QACD;YACE,SAAS,EAAE,aAAa,CAAC,SAAS;YAClC,KAAK,EAAE,aAAa,CAAC,UAAU;YAC/B,OAAO,EAAE,aAAa,CAAC,sBAAsB;YAC7C,OAAO,EAAE,IAAI;SACd;QACD;YACE,SAAS,EAAE,aAAa,CAAC,sBAAsB;YAC/C,KAAK,EAAE,aAAa,CAAC,mBAAmB;YACxC,OAAO,EAAE,aAAa,CAAC,gBAAgB;YACvC,OAAO,EAAE,IAAI;SACd;QACD;YACE,SAAS,EAAE,aAAa,CAAC,gBAAgB;YACzC,KAAK,EAAE,aAAa,CAAC,oBAAoB;YACzC,OAAO,EAAE,aAAa,CAAC,iBAAiB;YACxC,OAAO,EAAE,IAAI;SACd;QAED,iBAAiB;QACjB;YACE,SAAS,EAAE,aAAa,CAAC,SAAS;YAClC,KAAK,EAAE,aAAa,CAAC,cAAc;YACnC,OAAO,EAAE,aAAa,CAAC,+BAA+B;YACtD,OAAO,EAAE,IAAI;YACb,YAAY,EAAE,IAAI;YAClB,SAAS,EAAE,aAAa,CAAC,mBAAmB;YAC5C,KAAK,EAAE,SAAS,CAAC,QAAQ;SAC1B;QACD;YACE,SAAS,EAAE,aAAa,CAAC,+BAA+B;YACxD,KAAK,EAAE,aAAa,CAAC,mBAAmB;YACxC,OAAO,EAAE,aAAa,CAAC,gBAAgB;YACvC,OAAO,EAAE,IAAI;SACd;QAED,oBAAoB;QACpB;YACE,SAAS,EAAE,aAAa,CAAC,gBAAgB;YACzC,KAAK,EAAE,aAAa,CAAC,sBAAsB;YAC3C,OAAO,EAAE,aAAa,CAAC,kBAAkB;YACzC,OAAO,EAAE,IAAI;YACb,KAAK,EAAE,SAAS,CAAC,YAAY;SAC9B;QACD;YACE,SAAS,EAAE,aAAa,CAAC,kBAAkB;YAC3C,KAAK,EAAE,aAAa,CAAC,oBAAoB;YACzC,OAAO,EAAE,aAAa,CAAC,qBAAqB;YAC5C,OAAO,EAAE,IAAI;YACb,KAAK,EAAE,SAAS,CAAC,YAAY;SAC9B;QACD;YACE,SAAS,EAAE,aAAa,CAAC,qBAAqB;YAC9C,KAAK,EAAE,aAAa,CAAC,UAAU;YAC/B,OAAO,EAAE,aAAa,CAAC,sBAAsB;YAC7C,OAAO,EAAE,IAAI;SACd;QAED,cAAc;QACd;YACE,SAAS,EAAE,aAAa,CAAC,SAAS;YAClC,KAAK,EAAE,aAAa,CAAC,cAAc;YACnC,OAAO,EAAE,aAAa,CAAC,+BAA+B;YACtD,OAAO,EAAE,IAAI;YACb,YAAY,EAAE,IAAI;YAClB,SAAS,EAAE,aAAa,CAAC,mBAAmB;YAC5C,KAAK,EAAE,SAAS,CAAC,QAAQ;SAC1B;QACD;YACE,SAAS,EAAE,aAAa,CAAC,+BAA+B;YACxD,KAAK,EAAE,aAAa,CAAC,mBAAmB;YACxC,OAAO,EAAE,aAAa,CAAC,gBAAgB;YACvC,OAAO,EAAE,IAAI;SACd;QACD;YACE,SAAS,EAAE,aAAa,CAAC,gBAAgB;YACzC,KAAK,EAAE,aAAa,CAAC,sBAAsB;YAC3C,OAAO,EAAE,aAAa,CAAC,kBAAkB;YACzC,OAAO,EAAE,IAAI;YACb,KAAK,EAAE,SAAS,CAAC,QAAQ;SAC1B;KACF,CAAC;IAEM,MAAM,CAAU,oBAAoB,GAAG;QAC7C,yBAAyB;QACzB,CAAC,aAAa,CAAC,eAAe,CAAC,EAAE,SAAS;QAC1C,CAAC,aAAa,CAAC,kBAAkB,CAAC,EAAE,YAAY;QAChD,CAAC,aAAa,CAAC,iBAAiB,CAAC,EAAE,WAAW;QAC9C,CAAC,aAAa,CAAC,gBAAgB,CAAC,EAAE,UAAU;QAC5C,CAAC,aAAa,CAAC,gBAAgB,CAAC,EAAE,UAAU;QAC5C,CAAC,aAAa,CAAC,6BAA6B,CAAC,EAAE,YAAY;QAE3D,uBAAuB;QACvB,CAAC,aAAa,CAAC,SAAS,CAAC,EAAE,cAAc;QACzC,CAAC,aAAa,CAAC,qBAAqB,CAAC,EAAE,cAAc;QACrD,CAAC,aAAa,CAAC,sBAAsB,CAAC,EAAE,kBAAkB;QAC1D,CAAC,aAAa,CAAC,+BAA+B,CAAC,EAAE,kBAAkB;QACnE,CAAC,aAAa,CAAC,+BAA+B,CAAC,EAAE,kBAAkB;QACnE,CAAC,aAAa,CAAC,gBAAgB,CAAC,EAAE,YAAY;QAC9C,CAAC,aAAa,CAAC,mBAAmB,CAAC,EAAE,UAAU;QAC/C,CAAC,aAAa,CAAC,mBAAmB,CAAC,EAAE,UAAU;KAChD,CAAC;IAEF;;OAEG;IACH,MAAM,CAAC,aAAa,CAAC,SAAwB,EAAE,KAAoB;QACjE,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAC/B,CAAC,CAAC,SAAS,KAAK,SAAS,IAAI,CAAC,CAAC,KAAK,KAAK,KAAK,IAAI,CAAC,CAAC,OAAO,CAC5D,IAAI,IAAI,CAAC;IACZ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,gBAAgB,CAAC,aAA4B,EAAE,cAAsB;QAC1E,mDAAmD;QACnD,IAAI,cAAc,KAAK,OAAO,EAAE,CAAC;YAC/B,IAAI,aAAa,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;gBACvC,OAAO,IAAI,CAAC,oBAAoB,CAAC,aAAa,CAAC,IAAI,IAAI,CAAC;YAC1D,CAAC;QACH,CAAC;aAAM,CAAC;YACN,iCAAiC;YACjC,IAAI,aAAa,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE,CAAC;gBACzC,OAAO,IAAI,CAAC,oBAAoB,CAAC,aAAa,CAAC,IAAI,IAAI,CAAC;YAC1D,CAAC;QACH,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,aAAa,CAAC,SAAwB,EAAE,KAAoB;QACjE,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;QACxD,OAAO,UAAU,KAAK,IAAI,IAAI,UAAU,CAAC,OAAO,CAAC;IACnD,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,cAAc,CAAC,SAAwB;QAC5C,OAAO,IAAI,CAAC,WAAW;aACpB,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS,KAAK,SAAS,IAAI,CAAC,CAAC,OAAO,CAAC;aACnD,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;IACvB,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,YAAY,CAAC,aAA4B,EAAE,OAAgC;QAChF,IAAI,aAAa,KAAK,aAAa,CAAC,qBAAqB,EAAE,CAAC;YAC1D,OAAO,SAAS,CAAC,YAAY,CAAC;QAChC,CAAC;QACD,IAAI,aAAa,KAAK,aAAa,CAAC,mBAAmB,EAAE,CAAC;YACxD,OAAO,SAAS,CAAC,QAAQ,CAAC;QAC5B,CAAC;QACD,IAAI,aAAa,KAAK,aAAa,CAAC,mBAAmB,EAAE,CAAC;YACxD,OAAO,SAAS,CAAC,QAAQ,CAAC;QAC5B,CAAC;QACD,OAAO,SAAS,CAAC,IAAI,CAAC;IACxB,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,gBAAgB,CAAC,cAAsB;QAC5C,IAAI,cAAc,KAAK,OAAO,EAAE,CAAC;YAC/B,OAAO,CAAC,cAAc,EAAE,kBAAkB,EAAE,YAAY,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC;QACpF,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,SAAS,EAAE,YAAY,EAAE,WAAW,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC;QACxE,CAAC;IACH,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,iBAAiB,CAAC,OAAY,EAAE,cAAsB;QAC3D,MAAM,aAAa,GAAG,OAAO,CAAC,cAA+B,CAAC;QAE9D,IAAI,cAAc,KAAK,OAAO,EAAE,CAAC;YAC/B,OAAO,aAAa,KAAK,aAAa,CAAC,SAAS;gBACzC,aAAa,KAAK,aAAa,CAAC,qBAAqB;gBACrD,aAAa,KAAK,aAAa,CAAC,sBAAsB,CAAC;QAChE,CAAC;QAED,0CAA0C;QAC1C,OAAO,aAAa,KAAK,aAAa,CAAC,eAAe,CAAC;IACzD,CAAC;;AA3MH,kEA4MC"}