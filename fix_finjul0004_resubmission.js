const mysql = require('mysql2/promise');

async function fixFINJUL0004Resubmission() {
  console.log('🔧 FIXING FINJUL0004 RESUBMISSION FLAG');
  console.log('====================================');
  
  const connection = await mysql.createConnection({
    host: 'localhost',
    user: 'root',
    password: 'vms@2025@1989',
    database: 'vms_production'
  });
  
  try {
    // Find FINJUL0004 voucher that was processed with the old "Add Back" code
    const [vouchers] = await connection.execute(`
      SELECT id, voucher_id, status, workflow_state, is_resubmitted, rejected_by, rejection_time
      FROM vouchers 
      WHERE voucher_id = 'FINJUL0004' 
        AND status = 'PENDING SUBMISSION' 
        AND workflow_state = 'FINANCE_PENDING'
        AND is_resubmitted = 0
        AND rejected_by IS NOT NULL
        AND rejection_time IS NOT NULL
    `);
    
    if (vouchers.length === 0) {
      console.log('❌ No FINJUL0004 voucher found that needs fixing');
      console.log('   Either:');
      console.log('   - The voucher was not processed with "Add Back"');
      console.log('   - The voucher is already marked as resubmission');
      console.log('   - The voucher is not in the expected state');
      return;
    }
    
    const voucher = vouchers[0];
    console.log('📋 Found FINJUL0004 voucher that needs fixing:');
    console.log(`   ID: ${voucher.id}`);
    console.log(`   Status: ${voucher.status}`);
    console.log(`   Workflow State: ${voucher.workflow_state}`);
    console.log(`   Is Resubmitted: ${voucher.is_resubmitted} (should be 1)`);
    console.log(`   Rejected By: ${voucher.rejected_by}`);
    console.log(`   Rejection Time: ${voucher.rejection_time}`);
    
    console.log('\n🔧 Applying the fix...');
    
    // Fix the voucher by setting is_resubmitted = 1
    await connection.execute(`
      UPDATE vouchers 
      SET is_resubmitted = 1,
          last_resubmission_date = NOW()
      WHERE id = ?
    `, [voucher.id]);
    
    console.log('✅ Successfully fixed FINJUL0004 resubmission flag');
    
    // Verify the fix
    const [fixedVouchers] = await connection.execute(`
      SELECT id, voucher_id, status, workflow_state, is_resubmitted, last_resubmission_date
      FROM vouchers 
      WHERE id = ?
    `, [voucher.id]);
    
    const fixed = fixedVouchers[0];
    console.log('\n📊 AFTER FIX:');
    console.log(`   ID: ${fixed.id}`);
    console.log(`   Voucher ID: ${fixed.voucher_id}`);
    console.log(`   Status: ${fixed.status}`);
    console.log(`   Workflow State: ${fixed.workflow_state}`);
    console.log(`   Is Resubmitted: ${fixed.is_resubmitted} ✅`);
    console.log(`   Last Resubmission Date: ${fixed.last_resubmission_date}`);
    
    console.log('\n🎯 EXPECTED BEHAVIOR NOW:');
    console.log('✅ FINJUL0004 should show RESUBMISSION badge in Finance PENDING tab');
    console.log('✅ When sent to Audit, it will be detected as resubmission');
    console.log('✅ When certified and dispatched back, the resubmission fixes will apply');
    console.log('✅ It will appear in both Finance CERTIFIED and Audit DISPATCHED tabs');
    
  } catch (error) {
    console.error('❌ Error fixing FINJUL0004 resubmission:', error);
  } finally {
    await connection.end();
  }
}

fixFINJUL0004Resubmission().catch(console.error);
