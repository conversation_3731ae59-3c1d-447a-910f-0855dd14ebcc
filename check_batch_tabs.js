const mysql = require('mysql2/promise');

async function checkBatchState() {
  let connection;
  try {
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root', 
      password: 'vms@2025@1989',
      database: 'vms_production'
    });
    
    console.log('🔍 CHECKING BATCH STATE FOR TAB DETERMINATION');
    console.log('=============================================');
    
    // Get all batches
    const [batches] = await connection.execute(`
      SELECT
        id, department, sent_by, sent_time, received,
        contains_resubmissions, resubmission_count, created_at
      FROM voucher_batches
      ORDER BY created_at DESC
    `);
    
    if (batches.length === 0) {
      console.log('❌ No batches found');
      return;
    }
    
    console.log(`📦 Found ${batches.length} batch(es):`);
    
    for (const batch of batches) {
      console.log(`\n📋 BATCH ID: ${batch.id}`);
      console.log(`   Department: ${batch.department}`);
      console.log(`   Sent By: ${batch.sent_by}`);
      console.log(`   Sent Time: ${batch.sent_time}`);
      console.log(`   Received: ${batch.received ? 'YES' : 'NO'}`);
      console.log(`   Contains Resubmissions: ${batch.contains_resubmissions ? 'YES' : 'NO'}`);
      console.log(`   Resubmission Count: ${batch.resubmission_count || 0}`);
      
      // Determine which tab this batch should appear in
      console.log(`\n🎯 TAB DETERMINATION:`);
      
      if (!batch.received) {
        if (batch.contains_resubmissions) {
          console.log(`   ✅ Should appear in: RESUBMISSIONS tab`);
          console.log(`   📝 Reason: Batch not received + contains resubmissions`);
        } else {
          console.log(`   ✅ Should appear in: PENDING RECEIPT tab`);
          console.log(`   📝 Reason: Batch not received + no resubmissions`);
        }
      } else {
        console.log(`   ✅ Should appear in: RECEIVED tab`);
        console.log(`   📝 Reason: Batch already received`);
      }
      
      // Get vouchers in this batch
      const [vouchers] = await connection.execute(`
        SELECT voucher_id, claimant, status, workflow_state, is_resubmitted
        FROM vouchers 
        WHERE batch_id = ?
      `, [batch.id]);
      
      console.log(`\n📄 Vouchers in this batch (${vouchers.length}):`);
      vouchers.forEach((voucher, index) => {
        console.log(`   ${index + 1}. ${voucher.voucher_id} - ${voucher.claimant}`);
        console.log(`      Status: ${voucher.status}`);
        console.log(`      Workflow: ${voucher.workflow_state}`);
        console.log(`      Is Resubmitted: ${voucher.is_resubmitted ? 'YES' : 'NO'}`);
      });
    }
    
    console.log(`\n🎯 AUDIT TAB SUMMARY:`);
    const pendingBatches = batches.filter(b => !b.received && !b.contains_resubmissions);
    const resubmissionBatches = batches.filter(b => !b.received && b.contains_resubmissions);
    const receivedBatches = batches.filter(b => b.received);
    
    console.log(`   📥 PENDING RECEIPT tab: ${pendingBatches.length} batch(es)`);
    console.log(`   🔄 RESUBMISSIONS tab: ${resubmissionBatches.length} batch(es)`);
    console.log(`   ✅ RECEIVED tab: ${receivedBatches.length} batch(es)`);
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

checkBatchState();
