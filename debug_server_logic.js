const mysql = require('mysql2/promise');
require('dotenv').config();

async function debugServerLogic() {
  let connection;
  
  try {
    // Create connection
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || 'vms@2025@1989',
      database: process.env.DB_NAME || 'vms_production'
    });

    console.log('🔗 Connected to database');
    console.log('🔍 DEBUGGING SERVER LOGIC - Step by step analysis\n');

    // Get the exact voucher that was sent to audit
    const [vouchers] = await connection.execute(`
      SELECT 
        id,
        voucher_id, 
        claimant, 
        status, 
        workflow_state, 
        is_resubmitted, 
        sent_to_audit,
        batch_id,
        rejected_by,
        rejection_time,
        comment,
        department
      FROM vouchers 
      WHERE claimant = 'KAY' AND voucher_id = 'FINJUL0001'
    `);

    if (vouchers.length === 0) {
      console.log('❌ No KAY voucher found');
      return;
    }

    const voucher = vouchers[0];
    console.log('📊 VOUCHER DATA RETRIEVED FROM DATABASE:');
    console.log('==========================================');
    console.log(`ID: ${voucher.id}`);
    console.log(`Voucher ID: ${voucher.voucher_id}`);
    console.log(`Claimant: ${voucher.claimant}`);
    console.log(`Status: ${voucher.status}`);
    console.log(`Department: ${voucher.department}`);
    console.log(`Rejected By: "${voucher.rejected_by}" (type: ${typeof voucher.rejected_by})`);
    console.log(`Rejection Time: ${voucher.rejection_time} (type: ${typeof voucher.rejection_time})`);
    console.log(`Comment: ${voucher.comment}`);
    console.log(`Sent to Audit: ${voucher.sent_to_audit}`);
    console.log(`Is Resubmitted: ${voucher.is_resubmitted}`);

    console.log('\n🧪 TESTING SERVER RESUBMISSION DETECTION LOGIC:');
    console.log('================================================');
    
    // Exact logic from server
    const condition1 = (voucher.rejected_by && voucher.rejected_by.trim() !== '');
    const condition2 = (voucher.rejection_time);
    const condition3 = (voucher.status === 'VOUCHER REJECTED');
    const condition4 = (voucher.status === 'VOUCHER RETURNED');
    
    console.log(`Condition 1: (voucher.rejected_by && voucher.rejected_by.trim() !== '')`);
    console.log(`  voucher.rejected_by = "${voucher.rejected_by}"`);
    console.log(`  voucher.rejected_by.trim() = "${voucher.rejected_by ? voucher.rejected_by.trim() : 'N/A'}"`);
    console.log(`  Result: ${condition1}`);
    
    console.log(`\nCondition 2: (voucher.rejection_time)`);
    console.log(`  voucher.rejection_time = ${voucher.rejection_time}`);
    console.log(`  Result: ${condition2}`);
    
    console.log(`\nCondition 3: (voucher.status === 'VOUCHER REJECTED')`);
    console.log(`  voucher.status = "${voucher.status}"`);
    console.log(`  Result: ${condition3}`);
    
    console.log(`\nCondition 4: (voucher.status === 'VOUCHER RETURNED')`);
    console.log(`  voucher.status = "${voucher.status}"`);
    console.log(`  Result: ${condition4}`);
    
    const isResubmission = condition1 || condition2 || condition3 || condition4;
    console.log(`\n🎯 FINAL RESUBMISSION DETECTION:`);
    console.log(`isResubmission = ${condition1} || ${condition2} || ${condition3} || ${condition4} = ${isResubmission}`);
    
    // What should happen
    const targetStatus = isResubmission ? 'RE-SUBMISSION' : 'PENDING RECEIPT';
    const targetWorkflowState = isResubmission ? 'FINANCE_RESUBMISSION' : 'FINANCE_PROCESSING';
    const isResubmittedFlag = isResubmission ? 1 : 0;
    
    console.log(`\n📋 EXPECTED SERVER ACTIONS:`);
    console.log(`Target Status: ${targetStatus}`);
    console.log(`Target Workflow State: ${targetWorkflowState}`);
    console.log(`Is Resubmitted Flag: ${isResubmittedFlag}`);
    
    console.log(`\n📋 ACTUAL CURRENT STATE:`);
    console.log(`Current Status: ${voucher.status}`);
    console.log(`Current Workflow State: ${voucher.workflow_state}`);
    console.log(`Current Is Resubmitted: ${voucher.is_resubmitted}`);
    
    // Check if there's a mismatch
    const statusMatch = voucher.status === targetStatus;
    const workflowMatch = voucher.workflow_state === targetWorkflowState;
    const flagMatch = voucher.is_resubmitted === isResubmittedFlag;
    
    console.log(`\n🔍 MISMATCH ANALYSIS:`);
    console.log(`Status Match: ${statusMatch ? '✅' : '❌'} (Expected: ${targetStatus}, Actual: ${voucher.status})`);
    console.log(`Workflow Match: ${workflowMatch ? '✅' : '❌'} (Expected: ${targetWorkflowState}, Actual: ${voucher.workflow_state})`);
    console.log(`Flag Match: ${flagMatch ? '✅' : '❌'} (Expected: ${isResubmittedFlag}, Actual: ${voucher.is_resubmitted})`);
    
    if (!statusMatch || !workflowMatch || !flagMatch) {
      console.log(`\n❌ PROBLEM IDENTIFIED:`);
      console.log(`The server logic should detect this as a resubmission but the database shows it wasn't processed correctly.`);
      
      if (isResubmission) {
        console.log(`\n🔧 POSSIBLE CAUSES:`);
        console.log(`1. Server code has a bug in the UPDATE query`);
        console.log(`2. Database transaction failed or rolled back`);
        console.log(`3. Different code path was executed`);
        console.log(`4. Server is running old compiled code`);
        console.log(`5. There's an error in the server logic that prevents the update`);
      }
    } else {
      console.log(`\n✅ LOGIC IS WORKING CORRECTLY`);
    }

    // Check the batch that was created
    if (voucher.batch_id) {
      console.log(`\n📦 CHECKING BATCH: ${voucher.batch_id}`);
      const [batches] = await connection.execute(`
        SELECT 
          id,
          department,
          sent_by,
          contains_resubmissions,
          resubmission_count
        FROM voucher_batches 
        WHERE id = ?
      `, [voucher.batch_id]);

      if (batches.length > 0) {
        const batch = batches[0];
        const expectedContainsResubmissions = isResubmission ? 1 : 0;
        const expectedResubmissionCount = isResubmission ? 1 : 0;
        
        console.log(`Batch Department: ${batch.department}`);
        console.log(`Batch Sent By: ${batch.sent_by}`);
        console.log(`Contains Resubmissions: ${batch.contains_resubmissions} (Expected: ${expectedContainsResubmissions}) ${batch.contains_resubmissions === expectedContainsResubmissions ? '✅' : '❌'}`);
        console.log(`Resubmission Count: ${batch.resubmission_count || 0} (Expected: ${expectedResubmissionCount}) ${(batch.resubmission_count || 0) === expectedResubmissionCount ? '✅' : '❌'}`);
      }
    }

  } catch (error) {
    console.error('❌ Error debugging server logic:', error);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n🔌 Database connection closed');
    }
  }
}

// Run the debug
debugServerLogic();
